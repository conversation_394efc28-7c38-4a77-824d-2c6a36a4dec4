"""
Data Validation Module
Validates data quality and consistency across movie and review datasets
"""

import pandas as pd
import numpy as np
import json
import logging
from typing import List, Dict, Optional, Tuple
from pathlib import Path
import sys
from datetime import datetime
import re

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from config import PROCESSED_DATA_DIR, MIN_REVIEW_LENGTH
from 02_data_cleansing.utils.cleaning_utils import DataValidator

logger = logging.getLogger(__name__)

class DataQualityValidator:
    """Comprehensive data quality validation"""
    
    def __init__(self):
        self.validator = DataValidator()
        self.validation_report = {
            'movies': {},
            'reviews': {},
            'overall': {}
        }
    
    def validate_movies_dataset(self, movies_data: List[Dict]) -> Dict:
        """Validate movies dataset quality"""
        logger.info(f"Validating {len(movies_data)} movies")
        
        validation_results = {
            'total_movies': len(movies_data),
            'valid_movies': 0,
            'issues': {
                'missing_title': 0,
                'missing_year': 0,
                'invalid_year': 0,
                'missing_rating': 0,
                'invalid_rating': 0,
                'missing_genres': 0,
                'missing_plot': 0,
                'duplicate_titles': 0
            },
            'statistics': {},
            'recommendations': []
        }
        
        # Track seen titles for duplicate detection
        seen_titles = {}
        valid_movies = []
        
        for i, movie in enumerate(movies_data):
            movie_issues = self._validate_single_movie(movie)
            
            # Count issues
            for issue in movie_issues:
                if issue in validation_results['issues']:
                    validation_results['issues'][issue] += 1
            
            # Check for duplicates
            title = movie.get('title', '').lower().strip()
            year = movie.get('year')
            title_year_key = f"{title}_{year}"
            
            if title_year_key in seen_titles:
                validation_results['issues']['duplicate_titles'] += 1
            else:
                seen_titles[title_year_key] = i
                if not movie_issues:  # No issues found
                    valid_movies.append(movie)
        
        validation_results['valid_movies'] = len(valid_movies)
        
        # Calculate statistics
        validation_results['statistics'] = self._calculate_movie_statistics(movies_data)
        
        # Generate recommendations
        validation_results['recommendations'] = self._generate_movie_recommendations(validation_results)
        
        self.validation_report['movies'] = validation_results
        logger.info(f"Movie validation completed. {validation_results['valid_movies']}/{validation_results['total_movies']} movies are valid")
        
        return validation_results
    
    def _validate_single_movie(self, movie: Dict) -> List[str]:
        """Validate a single movie record"""
        issues = []
        
        # Check required fields
        if not movie.get('title'):
            issues.append('missing_title')
        
        # Validate year
        year = movie.get('year')
        if not year:
            issues.append('missing_year')
        elif not isinstance(year, int) or year < 1900 or year > 2030:
            issues.append('invalid_year')
        
        # Validate rating
        rating = movie.get('rating') or movie.get('vote_average') or movie.get('imdb_rating')
        if not rating:
            issues.append('missing_rating')
        elif not isinstance(rating, (int, float)) or rating < 0 or rating > 10:
            issues.append('invalid_rating')
        
        # Check genres
        genres = movie.get('genres', [])
        if not genres:
            issues.append('missing_genres')
        
        # Check plot/overview
        plot = movie.get('plot') or movie.get('overview')
        if not plot:
            issues.append('missing_plot')
        
        return issues
    
    def _calculate_movie_statistics(self, movies_data: List[Dict]) -> Dict:
        """Calculate statistics for movies dataset"""
        stats = {}
        
        try:
            # Year distribution
            years = [m.get('year') for m in movies_data if m.get('year')]
            if years:
                stats['year_range'] = [min(years), max(years)]
                stats['avg_year'] = sum(years) / len(years)
            
            # Rating distribution
            ratings = []
            for movie in movies_data:
                rating = movie.get('rating') or movie.get('vote_average') or movie.get('imdb_rating')
                if rating and isinstance(rating, (int, float)):
                    ratings.append(rating)
            
            if ratings:
                stats['rating_range'] = [min(ratings), max(ratings)]
                stats['avg_rating'] = sum(ratings) / len(ratings)
            
            # Genre distribution
            all_genres = []
            for movie in movies_data:
                genres = movie.get('genres', [])
                if isinstance(genres, list):
                    all_genres.extend(genres)
            
            genre_counts = {}
            for genre in all_genres:
                genre_counts[genre] = genre_counts.get(genre, 0) + 1
            
            stats['top_genres'] = sorted(genre_counts.items(), key=lambda x: x[1], reverse=True)[:10]
            
        except Exception as e:
            logger.warning(f"Error calculating movie statistics: {str(e)}")
        
        return stats
    
    def validate_reviews_dataset(self, reviews_data: List[Dict]) -> Dict:
        """Validate reviews dataset quality"""
        logger.info(f"Validating {len(reviews_data)} reviews")
        
        validation_results = {
            'total_reviews': len(reviews_data),
            'valid_reviews': 0,
            'issues': {
                'missing_text': 0,
                'short_text': 0,
                'missing_movie_id': 0,
                'invalid_rating': 0,
                'missing_source': 0,
                'duplicate_reviews': 0,
                'potential_spam': 0
            },
            'statistics': {},
            'recommendations': []
        }
        
        # Track seen reviews for duplicate detection
        seen_reviews = set()
        valid_reviews = []
        
        for review in reviews_data:
            review_issues = self._validate_single_review(review)
            
            # Count issues
            for issue in review_issues:
                if issue in validation_results['issues']:
                    validation_results['issues'][issue] += 1
            
            # Check for duplicates
            text = review.get('text', '').strip()
            movie_id = review.get('imdb_id') or review.get('tmdb_id') or review.get('movie_id')
            review_key = f"{text[:100]}_{movie_id}"  # Use first 100 chars + movie_id
            
            if review_key in seen_reviews:
                validation_results['issues']['duplicate_reviews'] += 1
            else:
                seen_reviews.add(review_key)
                if not review_issues:  # No issues found
                    valid_reviews.append(review)
        
        validation_results['valid_reviews'] = len(valid_reviews)
        
        # Calculate statistics
        validation_results['statistics'] = self._calculate_review_statistics(reviews_data)
        
        # Generate recommendations
        validation_results['recommendations'] = self._generate_review_recommendations(validation_results)
        
        self.validation_report['reviews'] = validation_results
        logger.info(f"Review validation completed. {validation_results['valid_reviews']}/{validation_results['total_reviews']} reviews are valid")
        
        return validation_results
    
    def _validate_single_review(self, review: Dict) -> List[str]:
        """Validate a single review record"""
        issues = []
        
        # Check text
        text = review.get('text', '')
        if not text:
            issues.append('missing_text')
        elif len(text) < MIN_REVIEW_LENGTH:
            issues.append('short_text')
        
        # Check movie ID
        movie_id = review.get('imdb_id') or review.get('tmdb_id') or review.get('movie_id')
        if not movie_id:
            issues.append('missing_movie_id')
        
        # Check rating
        rating = review.get('rating')
        if rating is not None:
            if not isinstance(rating, (int, float)) or rating < 0 or rating > 10:
                issues.append('invalid_rating')
        
        # Check source
        if not review.get('source'):
            issues.append('missing_source')
        
        # Check for potential spam
        if self._is_potential_spam(text):
            issues.append('potential_spam')
        
        return issues
    
    def _is_potential_spam(self, text: str) -> bool:
        """Check if text might be spam"""
        if not text:
            return False
        
        text_lower = text.lower()
        
        # Spam indicators
        spam_patterns = [
            r'click here',
            r'visit our website',
            r'buy now',
            r'free download',
            r'www\.',
            r'http[s]?://',
            r'call now',
            r'limited time offer'
        ]
        
        for pattern in spam_patterns:
            if re.search(pattern, text_lower):
                return True
        
        # Check for excessive repetition
        words = text_lower.split()
        if len(words) > 10:
            unique_words = set(words)
            if len(unique_words) / len(words) < 0.3:  # Less than 30% unique words
                return True
        
        return False
    
    def _calculate_review_statistics(self, reviews_data: List[Dict]) -> Dict:
        """Calculate statistics for reviews dataset"""
        stats = {}
        
        try:
            # Text length distribution
            text_lengths = [len(r.get('text', '')) for r in reviews_data]
            if text_lengths:
                stats['text_length'] = {
                    'min': min(text_lengths),
                    'max': max(text_lengths),
                    'avg': sum(text_lengths) / len(text_lengths),
                    'median': sorted(text_lengths)[len(text_lengths) // 2]
                }
            
            # Rating distribution
            ratings = [r.get('rating') for r in reviews_data if r.get('rating') is not None]
            if ratings:
                stats['rating'] = {
                    'min': min(ratings),
                    'max': max(ratings),
                    'avg': sum(ratings) / len(ratings),
                    'count': len(ratings)
                }
            
            # Source distribution
            sources = [r.get('source', 'unknown') for r in reviews_data]
            source_counts = {}
            for source in sources:
                source_counts[source] = source_counts.get(source, 0) + 1
            
            stats['sources'] = source_counts
            
        except Exception as e:
            logger.warning(f"Error calculating review statistics: {str(e)}")
        
        return stats
    
    def _generate_movie_recommendations(self, validation_results: Dict) -> List[str]:
        """Generate recommendations for improving movie data quality"""
        recommendations = []
        issues = validation_results['issues']
        total = validation_results['total_movies']
        
        if issues['missing_title'] > 0:
            recommendations.append(f"Remove {issues['missing_title']} movies without titles")
        
        if issues['missing_year'] / total > 0.1:
            recommendations.append("Consider enriching data with missing release years")
        
        if issues['missing_rating'] / total > 0.2:
            recommendations.append("Consider collecting ratings from additional sources")
        
        if issues['duplicate_titles'] > 0:
            recommendations.append(f"Remove {issues['duplicate_titles']} duplicate movies")
        
        return recommendations
    
    def _generate_review_recommendations(self, validation_results: Dict) -> List[str]:
        """Generate recommendations for improving review data quality"""
        recommendations = []
        issues = validation_results['issues']
        total = validation_results['total_reviews']
        
        if issues['short_text'] / total > 0.1:
            recommendations.append("Consider increasing minimum review length threshold")
        
        if issues['potential_spam'] > 0:
            recommendations.append(f"Review and remove {issues['potential_spam']} potential spam reviews")
        
        if issues['duplicate_reviews'] > 0:
            recommendations.append(f"Remove {issues['duplicate_reviews']} duplicate reviews")
        
        return recommendations
    
    def generate_validation_report(self, movies_data: List[Dict], reviews_data: List[Dict]) -> Dict:
        """Generate comprehensive validation report"""
        logger.info("Generating comprehensive validation report")
        
        # Validate datasets
        movie_validation = self.validate_movies_dataset(movies_data)
        review_validation = self.validate_reviews_dataset(reviews_data)
        
        # Overall statistics
        self.validation_report['overall'] = {
            'total_movies': len(movies_data),
            'total_reviews': len(reviews_data),
            'valid_movies': movie_validation['valid_movies'],
            'valid_reviews': review_validation['valid_reviews'],
            'movie_quality_score': movie_validation['valid_movies'] / len(movies_data) if movies_data else 0,
            'review_quality_score': review_validation['valid_reviews'] / len(reviews_data) if reviews_data else 0,
            'timestamp': datetime.now().isoformat()
        }
        
        # Save report
        self.save_validation_report()
        
        return self.validation_report
    
    def save_validation_report(self, filename: str = 'data_validation_report.json'):
        """Save validation report to file"""
        filepath = PROCESSED_DATA_DIR / filename
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.validation_report, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Validation report saved to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving validation report: {str(e)}")

if __name__ == "__main__":
    # Configure logging for standalone execution
    logging.basicConfig(level=logging.INFO)
    
    validator = DataQualityValidator()
    
    # Example usage with dummy data
    dummy_movies = [
        {'title': 'Test Movie', 'year': 2025, 'rating': 8.5, 'genres': ['Action']},
        {'title': '', 'year': 2025, 'rating': 7.0, 'genres': ['Drama']}  # Missing title
    ]
    
    dummy_reviews = [
        {'text': 'This is a great movie with excellent acting and plot.', 'imdb_id': 'tt1234567', 'source': 'imdb'},
        {'text': 'Bad', 'imdb_id': 'tt1234567', 'source': 'imdb'}  # Too short
    ]
    
    report = validator.generate_validation_report(dummy_movies, dummy_reviews)
    print("Validation report generated successfully")
