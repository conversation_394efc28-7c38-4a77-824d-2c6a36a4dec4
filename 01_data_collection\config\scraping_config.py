"""
Configuration for web scraping modules
"""

# IMDB Configuration
IMDB_CONFIG = {
    'base_url': 'https://www.imdb.com',
    'search_url': 'https://www.imdb.com/search/title/',
    'movie_url_template': 'https://www.imdb.com/title/{imdb_id}/',
    'reviews_url_template': 'https://www.imdb.com/title/{imdb_id}/reviews',
    'headers': {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
    },
    'request_delay': 1,  # seconds between requests
    'max_retries': 3,
    'timeout': 30
}

# TMDB Configuration
TMDB_CONFIG = {
    'base_url': 'https://api.themoviedb.org/3',
    'image_base_url': 'https://image.tmdb.org/t/p/w500',
    'endpoints': {
        'discover': '/discover/movie',
        'movie_details': '/movie/{movie_id}',
        'movie_reviews': '/movie/{movie_id}/reviews',
        'movie_credits': '/movie/{movie_id}/credits',
        'search': '/search/movie'
    },
    'default_params': {
        'language': 'en-US',
        'region': 'US'
    }
}

# Scraping Parameters
SCRAPING_PARAMS = {
    'target_year': 2025,
    'max_movies_per_genre': 50,
    'max_reviews_per_movie': 100,
    'min_review_length': 50,
    'genres': [
        'Action', 'Adventure', 'Animation', 'Comedy', 'Crime',
        'Documentary', 'Drama', 'Family', 'Fantasy', 'History',
        'Horror', 'Music', 'Mystery', 'Romance', 'Science Fiction',
        'TV Movie', 'Thriller', 'War', 'Western'
    ],
    'sort_options': [
        'popularity.desc',
        'release_date.desc',
        'vote_average.desc',
        'vote_count.desc'
    ]
}

# CSS Selectors for IMDB scraping
IMDB_SELECTORS = {
    'movie_title': 'h1[data-testid="hero-title-block__title"]',
    'movie_year': 'span.sc-8c396aa2-2',
    'movie_rating': 'span.sc-7ab21ed2-1',
    'movie_genres': 'div.ipc-chip-list__scroller a.ipc-chip',
    'movie_plot': 'span[data-testid="plot-xl"]',
    'movie_director': 'a[href*="/name/"]',
    'movie_cast': 'div[data-testid="title-cast"] a[data-testid="title-cast-item__actor"]',
    'review_container': 'div.review-container',
    'review_title': 'a.title',
    'review_text': 'div.text',
    'review_rating': 'span.rating-other-user-rating',
    'review_author': 'span.display-name-link',
    'review_date': 'span.review-date'
}

# XPath selectors as backup
IMDB_XPATHS = {
    'movie_title': '//h1[@data-testid="hero-title-block__title"]',
    'movie_rating': '//span[@class="sc-7ab21ed2-1"]',
    'review_containers': '//div[@class="review-container"]',
    'load_more_reviews': '//button[contains(text(), "Load More")]'
}

# Error handling configuration
ERROR_CONFIG = {
    'max_consecutive_failures': 5,
    'retry_delay': 2,  # seconds
    'backoff_factor': 2,
    'timeout_errors': ['TimeoutError', 'ConnectTimeout', 'ReadTimeout'],
    'rate_limit_errors': ['429', 'Too Many Requests'],
    'rate_limit_delay': 60  # seconds to wait on rate limit
}
