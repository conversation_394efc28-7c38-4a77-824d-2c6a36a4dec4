"""
Unit Tests for IMDB Aspect-Based Sentiment Analysis Project
"""

import unittest
import sys
from pathlib import Path
import json
import tempfile
import os

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

# Import modules to test
from 02_data_cleansing.utils.cleaning_utils import <PERSON><PERSON><PERSON><PERSON>, DataValidator
from 04_sentiment_analysis.01_aspect_extraction import AspectExtractor
from 04_sentiment_analysis.03_aspect_sentiment import AspectSentimentAnalyzer

class TestTextCleaner(unittest.TestCase):
    """Test TextCleaner functionality"""
    
    def setUp(self):
        self.cleaner = TextCleaner()
    
    def test_remove_html_tags(self):
        """Test HTML tag removal"""
        html_text = "<p>This is a <strong>great</strong> movie!</p>"
        expected = "This is a great movie!"
        result = self.cleaner.remove_html_tags(html_text)
        self.assertEqual(result, expected)
    
    def test_expand_contractions(self):
        """Test contraction expansion"""
        text = "I can't believe it's so good!"
        result = self.cleaner.expand_contractions(text)
        self.assertIn("cannot", result)
        self.assertIn("it is", result)
    
    def test_normalize_whitespace(self):
        """Test whitespace normalization"""
        text = "This   has    multiple   spaces"
        expected = "This has multiple spaces"
        result = self.cleaner.normalize_whitespace(text)
        self.assertEqual(result, expected)
    
    def test_remove_urls(self):
        """Test URL removal"""
        text = "Check out https://example.com for more info"
        result = self.cleaner.remove_urls(text)
        self.assertNotIn("https://example.com", result)
    
    def test_comprehensive_cleaning(self):
        """Test comprehensive text cleaning"""
        dirty_text = "<p>I can't believe this movie's so good! Visit https://example.com</p>"
        result = self.cleaner.clean_text_comprehensive(dirty_text)
        
        # Should not contain HTML tags, URLs, and contractions should be expanded
        self.assertNotIn("<p>", result)
        self.assertNotIn("https://", result)
        self.assertIn("cannot", result)

class TestDataValidator(unittest.TestCase):
    """Test DataValidator functionality"""
    
    def setUp(self):
        self.validator = DataValidator()
    
    def test_validate_movie_data(self):
        """Test movie data validation"""
        movie_data = {
            'title': 'Test Movie',
            'year': 2025,
            'rating': 8.5,
            'genres': ['Action', 'Drama']
        }
        
        result = self.validator.validate_movie_data(movie_data)
        
        self.assertEqual(result['title'], 'Test Movie')
        self.assertEqual(result['year'], 2025)
        self.assertEqual(result['rating'], 8.5)
        self.assertEqual(result['genres'], ['Action', 'Drama'])
    
    def test_validate_invalid_movie_data(self):
        """Test validation with invalid movie data"""
        invalid_movie = {
            'title': '',
            'year': 'invalid',
            'rating': 15,  # Invalid rating
            'genres': 'Action,Drama'  # String instead of list
        }
        
        result = self.validator.validate_movie_data(invalid_movie)
        
        self.assertEqual(result['title'], '')
        self.assertIsNone(result.get('year'))
        self.assertIsNone(result.get('rating'))
        self.assertEqual(result['genres'], ['Action', 'Drama'])  # Should be converted to list
    
    def test_validate_review_data(self):
        """Test review data validation"""
        review_data = {
            'text': 'This is a great movie with excellent acting!',
            'rating': 9,
            'source': 'imdb',
            'imdb_id': 'tt1234567'
        }
        
        result = self.validator.validate_review_data(review_data)
        
        self.assertEqual(result['text'], review_data['text'])
        self.assertEqual(result['rating'], 9)
        self.assertEqual(result['source'], 'imdb')
        self.assertEqual(result['imdb_id'], 'tt1234567')
    
    def test_is_valid_review(self):
        """Test review validity check"""
        valid_review = {
            'text': 'This is a sufficiently long review with meaningful content about the movie.'
        }
        
        invalid_review = {
            'text': 'Short'
        }
        
        self.assertTrue(self.validator.is_valid_review(valid_review))
        self.assertFalse(self.validator.is_valid_review(invalid_review))

class TestAspectExtractor(unittest.TestCase):
    """Test AspectExtractor functionality"""
    
    def setUp(self):
        self.extractor = AspectExtractor()
    
    def test_extract_aspects_from_text(self):
        """Test aspect extraction from text"""
        text = "The acting was excellent and the plot was engaging. The cinematography was beautiful."
        
        aspects = self.extractor.extract_aspects_from_text(text)
        
        # Should find acting, plot, and cinematography aspects
        self.assertIn('acting', aspects)
        self.assertIn('plot', aspects)
        self.assertIn('cinematography', aspects)
    
    def test_map_noun_to_aspect(self):
        """Test noun to aspect mapping"""
        self.assertEqual(self.extractor._map_noun_to_aspect('acting'), 'acting')
        self.assertEqual(self.extractor._map_noun_to_aspect('performance'), 'acting')
        self.assertEqual(self.extractor._map_noun_to_aspect('story'), 'plot')
        self.assertEqual(self.extractor._map_noun_to_aspect('director'), 'direction')
        self.assertIsNone(self.extractor._map_noun_to_aspect('unknown'))
    
    def test_find_sentences_with_keyword(self):
        """Test sentence extraction with keywords"""
        text = "The acting was great. The plot was confusing. Overall good movie."
        sentences = self.extractor._find_sentences_with_keyword(text, 'acting')
        
        self.assertEqual(len(sentences), 1)
        self.assertIn('acting was great', sentences[0].lower())

class TestAspectSentimentAnalyzer(unittest.TestCase):
    """Test AspectSentimentAnalyzer functionality"""
    
    def setUp(self):
        self.analyzer = AspectSentimentAnalyzer()
    
    def test_analyze_text_sentiment(self):
        """Test text sentiment analysis"""
        positive_text = "This movie is absolutely amazing and wonderful!"
        negative_text = "This movie is terrible and awful."
        neutral_text = "This movie is okay."
        
        pos_result = self.analyzer.analyze_text_sentiment(positive_text)
        neg_result = self.analyzer.analyze_text_sentiment(negative_text)
        neu_result = self.analyzer.analyze_text_sentiment(neutral_text)
        
        # Check that results contain expected keys
        self.assertIn('combined', pos_result)
        self.assertIn('label', pos_result['combined'])
        
        # Positive text should have positive sentiment
        self.assertEqual(pos_result['combined']['label'], 'positive')
        
        # Negative text should have negative sentiment
        self.assertEqual(neg_result['combined']['label'], 'negative')
    
    def test_get_vader_label(self):
        """Test VADER label assignment"""
        self.assertEqual(self.analyzer._get_vader_label(0.5), 'positive')
        self.assertEqual(self.analyzer._get_vader_label(-0.5), 'negative')
        self.assertEqual(self.analyzer._get_vader_label(0.05), 'neutral')
    
    def test_get_textblob_label(self):
        """Test TextBlob label assignment"""
        self.assertEqual(self.analyzer._get_textblob_label(0.5), 'positive')
        self.assertEqual(self.analyzer._get_textblob_label(-0.5), 'negative')
        self.assertEqual(self.analyzer._get_textblob_label(0.05), 'neutral')
    
    def test_analyze_aspect_sentiment(self):
        """Test aspect sentiment analysis"""
        review = {
            'text': 'The acting was excellent but the plot was confusing.',
            'extracted_aspects': {
                'acting': ['The acting was excellent'],
                'plot': ['the plot was confusing']
            }
        }
        
        result = self.analyzer.analyze_aspect_sentiment(review)
        
        self.assertIn('aspect_sentiments', result)
        self.assertIn('overall_sentiment', result)
        self.assertIn('acting', result['aspect_sentiments'])
        self.assertIn('plot', result['aspect_sentiments'])

class TestIntegration(unittest.TestCase):
    """Integration tests for the complete pipeline"""
    
    def test_text_processing_pipeline(self):
        """Test complete text processing pipeline"""
        # Sample data
        raw_text = "<p>I can't believe this movie's so amazing! The acting was great.</p>"
        
        # Clean text
        cleaner = TextCleaner()
        cleaned_text = cleaner.clean_text_comprehensive(raw_text)
        
        # Extract aspects
        extractor = AspectExtractor()
        aspects = extractor.extract_aspects_from_text(cleaned_text)
        
        # Analyze sentiment
        analyzer = AspectSentimentAnalyzer()
        sentiment = analyzer.analyze_text_sentiment(cleaned_text)
        
        # Verify pipeline worked
        self.assertNotIn('<p>', cleaned_text)
        self.assertIn('acting', aspects)
        self.assertIn('combined', sentiment)
    
    def test_data_validation_pipeline(self):
        """Test data validation pipeline"""
        # Sample movie and review data
        movie_data = {
            'title': 'Test Movie',
            'year': 2025,
            'rating': 8.5,
            'genres': ['Action']
        }
        
        review_data = {
            'text': 'This is a great movie with excellent acting and plot!',
            'rating': 9,
            'source': 'test'
        }
        
        # Validate data
        validator = DataValidator()
        validated_movie = validator.validate_movie_data(movie_data)
        validated_review = validator.validate_review_data(review_data)
        
        # Check validation worked
        self.assertEqual(validated_movie['title'], 'Test Movie')
        self.assertTrue(validator.is_valid_review(validated_review))

def create_test_data():
    """Create temporary test data files"""
    test_data_dir = Path(tempfile.mkdtemp())
    
    # Sample movies data
    movies_data = [
        {
            'title': 'Test Movie 1',
            'year': 2025,
            'rating': 8.5,
            'genres': ['Action', 'Drama'],
            'imdb_id': 'tt1234567'
        },
        {
            'title': 'Test Movie 2',
            'year': 2025,
            'rating': 7.2,
            'genres': ['Comedy'],
            'imdb_id': 'tt7654321'
        }
    ]
    
    # Sample reviews data
    reviews_data = [
        {
            'text': 'This movie has excellent acting and a great plot!',
            'rating': 9,
            'source': 'test',
            'imdb_id': 'tt1234567'
        },
        {
            'text': 'The cinematography was beautiful but the dialogue was poor.',
            'rating': 6,
            'source': 'test',
            'imdb_id': 'tt1234567'
        }
    ]
    
    # Save test data
    with open(test_data_dir / 'test_movies.json', 'w') as f:
        json.dump(movies_data, f)
    
    with open(test_data_dir / 'test_reviews.json', 'w') as f:
        json.dump(reviews_data, f)
    
    return test_data_dir

if __name__ == '__main__':
    # Run tests
    unittest.main(verbosity=2)
