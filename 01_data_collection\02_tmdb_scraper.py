"""
TMDB (The Movie Database) API Scraper
Collects movie data using TMDB API for 2025 movies
"""

import requests
import pandas as pd
import json
import logging
from typing import List, Dict, Optional
from datetime import datetime
import time
from pathlib import Path
import sys

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from config import RAW_DATA_DIR, TARGET_YEAR, TMDB_API_KEY
from 01_data_collection.config.scraping_config import TMDB_CONFIG, SCRAPING_PARAMS

logger = logging.getLogger(__name__)

class TMDBScraper:
    """Scraper for TMDB movie data using official API"""
    
    def __init__(self, api_key: str = TMDB_API_KEY):
        self.api_key = api_key
        self.base_url = TMDB_CONFIG['base_url']
        self.session = requests.Session()
        self.movies_data = []
        
        if not self.api_key or self.api_key == 'your_tmdb_api_key_here':
            logger.warning("TMDB API key not configured. Please set TMDB_API_KEY environment variable.")
    
    def _make_request(self, endpoint: str, params: Dict = None) -> Optional[Dict]:
        """Make API request to TMDB"""
        if not self.api_key or self.api_key == 'your_tmdb_api_key_here':
            logger.error("TMDB API key not configured")
            return None
            
        url = f"{self.base_url}{endpoint}"
        
        # Add API key and default params
        request_params = {'api_key': self.api_key}
        request_params.update(TMDB_CONFIG['default_params'])
        
        if params:
            request_params.update(params)
        
        try:
            response = self.session.get(url, params=request_params, timeout=30)
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Error making TMDB API request to {endpoint}: {str(e)}")
            return None
    
    def discover_movies_by_year(self, year: int = TARGET_YEAR) -> List[Dict]:
        """Discover movies by release year"""
        logger.info(f"Discovering movies for year {year} from TMDB")
        
        all_movies = []
        page = 1
        max_pages = 10  # Limit to avoid excessive API calls
        
        while page <= max_pages:
            params = {
                'primary_release_year': year,
                'sort_by': 'popularity.desc',
                'page': page,
                'vote_count.gte': 10  # Movies with at least 10 votes
            }
            
            data = self._make_request(TMDB_CONFIG['endpoints']['discover'], params)
            
            if not data or 'results' not in data:
                break
            
            movies = data['results']
            if not movies:
                break
            
            all_movies.extend(movies)
            logger.info(f"Collected {len(movies)} movies from page {page}")
            
            # Check if we've reached the last page
            if page >= data.get('total_pages', 1):
                break
                
            page += 1
            time.sleep(0.25)  # Rate limiting
        
        logger.info(f"Total movies discovered: {len(all_movies)}")
        return all_movies
    
    def get_movie_details(self, movie_id: int) -> Optional[Dict]:
        """Get detailed information for a specific movie"""
        endpoint = TMDB_CONFIG['endpoints']['movie_details'].format(movie_id=movie_id)
        
        params = {
            'append_to_response': 'credits,reviews,keywords,videos'
        }
        
        return self._make_request(endpoint, params)
    
    def get_movie_reviews(self, movie_id: int) -> List[Dict]:
        """Get reviews for a specific movie"""
        endpoint = TMDB_CONFIG['endpoints']['movie_reviews'].format(movie_id=movie_id)
        
        all_reviews = []
        page = 1
        max_pages = 5  # Limit review pages
        
        while page <= max_pages:
            params = {'page': page}
            data = self._make_request(endpoint, params)
            
            if not data or 'results' not in data:
                break
            
            reviews = data['results']
            if not reviews:
                break
            
            all_reviews.extend(reviews)
            
            if page >= data.get('total_pages', 1):
                break
                
            page += 1
            time.sleep(0.25)
        
        return all_reviews
    
    def enrich_movie_data(self, movies: List[Dict]) -> List[Dict]:
        """Enrich basic movie data with detailed information"""
        logger.info(f"Enriching {len(movies)} movies with detailed data")
        
        enriched_movies = []
        
        for i, movie in enumerate(movies):
            movie_id = movie.get('id')
            if not movie_id:
                continue
            
            logger.info(f"Processing movie {i+1}/{len(movies)}: {movie.get('title', 'Unknown')}")
            
            # Get detailed movie info
            detailed_info = self.get_movie_details(movie_id)
            if detailed_info:
                # Merge basic and detailed info
                enriched_movie = {**movie, **detailed_info}
                
                # Get reviews
                reviews = self.get_movie_reviews(movie_id)
                enriched_movie['tmdb_reviews'] = reviews
                enriched_movie['review_count'] = len(reviews)
                
                enriched_movies.append(enriched_movie)
            else:
                enriched_movies.append(movie)
            
            time.sleep(0.25)  # Rate limiting
        
        return enriched_movies
    
    def get_2025_movies(self) -> List[Dict]:
        """Get comprehensive 2025 movies data"""
        logger.info("Starting TMDB data collection for 2025 movies")
        
        # Discover movies
        movies = self.discover_movies_by_year(TARGET_YEAR)
        
        if not movies:
            logger.warning("No movies found")
            return []
        
        # Enrich with detailed data
        enriched_movies = self.enrich_movie_data(movies)
        
        # Save data
        self.save_movies_data(enriched_movies)
        
        logger.info(f"TMDB data collection completed. Collected {len(enriched_movies)} movies")
        return enriched_movies
    
    def save_movies_data(self, movies_data: List[Dict], filename: str = 'tmdb_movies_2025.json'):
        """Save movies data to file"""
        filepath = RAW_DATA_DIR / filename
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(movies_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Saved {len(movies_data)} movies to {filepath}")
            
            # Create a simplified CSV version
            simplified_data = []
            for movie in movies_data:
                simplified = {
                    'tmdb_id': movie.get('id'),
                    'imdb_id': movie.get('imdb_id'),
                    'title': movie.get('title'),
                    'release_date': movie.get('release_date'),
                    'vote_average': movie.get('vote_average'),
                    'vote_count': movie.get('vote_count'),
                    'popularity': movie.get('popularity'),
                    'overview': movie.get('overview'),
                    'genres': [g.get('name') for g in movie.get('genres', [])],
                    'runtime': movie.get('runtime'),
                    'budget': movie.get('budget'),
                    'revenue': movie.get('revenue'),
                    'review_count': movie.get('review_count', 0)
                }
                simplified_data.append(simplified)
            
            df = pd.DataFrame(simplified_data)
            csv_filepath = filepath.with_suffix('.csv')
            df.to_csv(csv_filepath, index=False, encoding='utf-8')
            logger.info(f"Also saved simplified CSV: {csv_filepath}")
            
        except Exception as e:
            logger.error(f"Error saving movies data: {str(e)}")
    
    def search_movie_by_title(self, title: str) -> Optional[Dict]:
        """Search for a specific movie by title"""
        params = {'query': title}
        data = self._make_request(TMDB_CONFIG['endpoints']['search'], params)
        
        if data and 'results' in data and data['results']:
            return data['results'][0]  # Return first result
        
        return None

if __name__ == "__main__":
    # Configure logging for standalone execution
    logging.basicConfig(level=logging.INFO)
    
    scraper = TMDBScraper()
    movies_data = scraper.get_2025_movies()
    print(f"Scraped {len(movies_data)} movies from TMDB")
