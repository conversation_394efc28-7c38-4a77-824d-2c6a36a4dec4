#!/usr/bin/env python3
"""
IMDB Scraping Test Script
Tests IMDB scraping functionality with proper anti-detection measures
"""

import sys
import time
import requests
import pandas as pd
import json
from pathlib import Path
from datetime import datetime
from bs4 import BeautifulSoup
import random
from urllib.parse import urljoin, quote
import re
from tqdm import tqdm

# Add project root to path
sys.path.append(str(Path(__file__).parent))

try:
    from config import RAW_DATA_DIR
except ImportError:
    RAW_DATA_DIR = Path("data/raw")

class IMDBScraper:
    """Enhanced IMDB scraper with anti-detection measures"""
    
    def __init__(self):
        self.base_url = "https://www.imdb.com"
        self.session = requests.Session()
        
        # Rotate user agents to avoid detection
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
        ]
        
        # Set initial headers
        self.update_headers()
        
        # Ensure data directory exists
        RAW_DATA_DIR.mkdir(parents=True, exist_ok=True)
        
        print("🎭 IMDB Scraper Initialized")
        print(f"Data Directory: {RAW_DATA_DIR}")
    
    def update_headers(self):
        """Update session headers with random user agent"""
        headers = {
            'User-Agent': random.choice(self.user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }
        self.session.headers.update(headers)
    
    def make_request(self, url, retries=3, delay=None):
        """Make request with retry logic and random delays"""
        for attempt in range(retries):
            try:
                # Random delay between requests
                if delay is None:
                    time.sleep(random.uniform(1, 3))
                else:
                    time.sleep(delay)
                
                # Update headers for each request
                self.update_headers()
                
                response = self.session.get(url, timeout=15)
                
                if response.status_code == 200:
                    return response
                elif response.status_code == 429:
                    print(f"⚠️ Rate limited, waiting longer...")
                    time.sleep(random.uniform(5, 10))
                    continue
                elif response.status_code in [403, 503]:
                    print(f"⚠️ Access denied ({response.status_code}), trying different approach...")
                    time.sleep(random.uniform(3, 7))
                    continue
                else:
                    print(f"⚠️ Status code {response.status_code} for {url}")
                    
            except requests.exceptions.RequestException as e:
                print(f"❌ Request error (attempt {attempt + 1}): {str(e)}")
                if attempt < retries - 1:
                    time.sleep(random.uniform(2, 5))
        
        return None
    
    def test_basic_connectivity(self):
        """Test basic IMDB connectivity"""
        print("\n🔍 Testing IMDB Basic Connectivity...")
        print("=" * 50)
        
        # Test main page
        response = self.make_request(self.base_url)
        if response:
            print("✅ IMDB main page accessible")
            if "IMDb" in response.text:
                print("✅ Page content looks correct")
                return True
            else:
                print("⚠️ Page content might be blocked")
        else:
            print("❌ Cannot access IMDB main page")
        
        return False
    
    def test_movie_page_access(self):
        """Test accessing specific movie pages"""
        print("\n🎬 Testing Movie Page Access...")
        print("=" * 50)
        
        # Test with known movie IDs
        test_movies = [
            ('tt0111161', 'The Shawshank Redemption'),
            ('tt0137523', 'Fight Club'),
            ('tt0068646', 'The Godfather')
        ]
        
        successful_access = 0
        
        for movie_id, title in test_movies:
            print(f"\n🔍 Testing: {title} ({movie_id})")
            url = f"{self.base_url}/title/{movie_id}/"
            
            response = self.make_request(url)
            if response:
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # Check if we can find the movie title
                title_element = soup.find('h1', {'data-testid': 'hero__pageTitle'})
                if not title_element:
                    title_element = soup.find('h1', class_='titleBar-title')
                if not title_element:
                    title_element = soup.find('h1')
                
                if title_element and title.lower() in title_element.get_text().lower():
                    print(f"✅ Successfully accessed {title}")
                    successful_access += 1
                    
                    # Try to extract basic info
                    self.extract_movie_info(soup, movie_id)
                else:
                    print(f"⚠️ Page accessed but content might be blocked")
            else:
                print(f"❌ Failed to access {title}")
        
        success_rate = successful_access / len(test_movies)
        print(f"\n📊 Movie page access success rate: {success_rate:.1%}")
        return success_rate > 0.5
    
    def extract_movie_info(self, soup, movie_id):
        """Extract basic movie information from soup"""
        try:
            # Try to find rating
            rating_element = soup.find('span', class_='sc-bde20123-1')
            if not rating_element:
                rating_element = soup.find('span', {'data-testid': 'rating-button__aggregate-rating__score'})
            
            if rating_element:
                rating = rating_element.get_text().strip()
                print(f"   Rating: {rating}")
            
            # Try to find year
            year_elements = soup.find_all('a', href=re.compile(r'/year/\d{4}/'))
            if year_elements:
                year = year_elements[0].get_text().strip()
                print(f"   Year: {year}")
            
            # Try to find genres
            genre_elements = soup.find_all('a', href=re.compile(r'/search/title/\?genres='))
            if genre_elements:
                genres = [g.get_text().strip() for g in genre_elements[:3]]
                print(f"   Genres: {', '.join(genres)}")
                
        except Exception as e:
            print(f"   ⚠️ Error extracting info: {str(e)}")
    
    def test_reviews_access(self):
        """Test accessing movie reviews"""
        print("\n📝 Testing Reviews Access...")
        print("=" * 50)
        
        # Test reviews for a popular movie
        movie_id = 'tt0111161'  # Shawshank Redemption
        reviews_url = f"{self.base_url}/title/{movie_id}/reviews"
        
        print(f"🔍 Testing reviews for {movie_id}")
        response = self.make_request(reviews_url)
        
        if response:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Look for review elements
            review_containers = soup.find_all('div', class_='review-container')
            if not review_containers:
                review_containers = soup.find_all('div', {'data-testid': 'review-card'})
            if not review_containers:
                review_containers = soup.find_all('div', class_='lister-item')
            
            if review_containers:
                print(f"✅ Found {len(review_containers)} review containers")
                
                # Try to extract a sample review
                sample_review = self.extract_sample_review(review_containers[0])
                if sample_review:
                    print("✅ Successfully extracted sample review")
                    print(f"   Author: {sample_review.get('author', 'Unknown')}")
                    print(f"   Rating: {sample_review.get('rating', 'N/A')}")
                    text = sample_review.get('text', '')
                    print(f"   Text: {text[:100]}{'...' if len(text) > 100 else ''}")
                    return True
                else:
                    print("⚠️ Found containers but couldn't extract review data")
            else:
                print("⚠️ No review containers found")
                # Check if page has any review-related content
                if 'review' in response.text.lower():
                    print("⚠️ Page contains review-related content but structure might have changed")
                else:
                    print("❌ No review content detected")
        else:
            print("❌ Failed to access reviews page")
        
        return False
    
    def extract_sample_review(self, review_container):
        """Extract review data from a review container"""
        try:
            review_data = {}
            
            # Try to find author
            author_element = review_container.find('span', class_='display-name-link')
            if not author_element:
                author_element = review_container.find('a', href=re.compile(r'/user/'))
            if author_element:
                review_data['author'] = author_element.get_text().strip()
            
            # Try to find rating
            rating_element = review_container.find('span', class_='rating-other-user-rating')
            if not rating_element:
                rating_element = review_container.find('span', class_='point-scale')
            if rating_element:
                rating_text = rating_element.get_text().strip()
                rating_match = re.search(r'(\d+)', rating_text)
                if rating_match:
                    review_data['rating'] = int(rating_match.group(1))
            
            # Try to find review text
            text_element = review_container.find('div', class_='text')
            if not text_element:
                text_element = review_container.find('div', class_='content')
            if text_element:
                review_data['text'] = text_element.get_text().strip()
            
            # Try to find date
            date_element = review_container.find('span', class_='review-date')
            if date_element:
                review_data['date'] = date_element.get_text().strip()
            
            return review_data if review_data else None
            
        except Exception as e:
            print(f"   ⚠️ Error extracting review: {str(e)}")
            return None
    
    def test_search_functionality(self):
        """Test IMDB search functionality"""
        print("\n🔍 Testing Search Functionality...")
        print("=" * 50)
        
        # Test searching for 2025 movies
        search_query = "2025"
        search_url = f"{self.base_url}/find?q={quote(search_query)}&s=tt&ttype=ft&ref_=fn_ft"
        
        print(f"🔍 Searching for: {search_query}")
        response = self.make_request(search_url)
        
        if response:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Look for search results
            result_elements = soup.find_all('td', class_='result_text')
            if not result_elements:
                result_elements = soup.find_all('li', class_='find-result')
            
            if result_elements:
                print(f"✅ Found {len(result_elements)} search results")
                
                # Extract sample results
                movies_found = []
                for i, element in enumerate(result_elements[:5]):
                    try:
                        link = element.find('a')
                        if link and '/title/tt' in link.get('href', ''):
                            title = link.get_text().strip()
                            href = link.get('href')
                            movie_id = re.search(r'tt\d+', href)
                            if movie_id:
                                movies_found.append({
                                    'title': title,
                                    'imdb_id': movie_id.group(),
                                    'url': urljoin(self.base_url, href)
                                })
                    except Exception as e:
                        continue
                
                if movies_found:
                    print(f"✅ Successfully parsed {len(movies_found)} movie results")
                    for movie in movies_found[:3]:
                        print(f"   - {movie['title']} ({movie['imdb_id']})")
                    return True
                else:
                    print("⚠️ Found results but couldn't parse movie data")
            else:
                print("⚠️ No search results found")
        else:
            print("❌ Failed to access search page")
        
        return False
    
    def run_comprehensive_test(self):
        """Run comprehensive IMDB scraping test"""
        print("🧪 IMDB Scraping Comprehensive Test")
        print("=" * 60)
        print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        results = {}
        
        # Test 1: Basic connectivity
        results['connectivity'] = self.test_basic_connectivity()
        
        # Test 2: Movie page access
        results['movie_pages'] = self.test_movie_page_access()
        
        # Test 3: Reviews access
        results['reviews'] = self.test_reviews_access()
        
        # Test 4: Search functionality
        results['search'] = self.test_search_functionality()
        
        # Summary
        print("\n" + "=" * 60)
        print("🏁 IMDB Scraping Test Summary")
        print("=" * 60)
        
        for test_name, success in results.items():
            status = "✅ PASSED" if success else "❌ FAILED"
            print(f"{test_name.upper().replace('_', ' ')}: {status}")
        
        total_passed = sum(results.values())
        total_tests = len(results)
        
        print(f"\nOverall: {total_passed}/{total_tests} tests passed")
        
        # Recommendations
        print("\n📋 Recommendations:")
        if results['connectivity']:
            print("✅ Basic IMDB access is working")
        else:
            print("❌ Basic connectivity issues - check network/proxy")
        
        if results['movie_pages']:
            print("✅ Movie page scraping is functional")
        else:
            print("⚠️ Movie page access limited - may need enhanced anti-detection")
        
        if results['reviews']:
            print("✅ Review scraping is working")
        else:
            print("⚠️ Review scraping blocked - IMDB has strong anti-bot measures")
        
        if results['search']:
            print("✅ Search functionality is working")
        else:
            print("⚠️ Search access limited")
        
        # Overall assessment
        if total_passed >= 3:
            print("\n🎉 IMDB scraping is largely functional!")
            print("💡 Consider implementing additional anti-detection measures for production use")
        elif total_passed >= 2:
            print("\n⚠️ IMDB scraping has some limitations")
            print("💡 May need enhanced techniques for full functionality")
        else:
            print("\n❌ IMDB scraping is heavily restricted")
            print("💡 Consider alternative data sources or advanced scraping techniques")
        
        # Save test results
        self.save_test_results(results)
        
        return results
    
    def save_test_results(self, results):
        """Save test results to file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_data = {
            'test_timestamp': datetime.now().isoformat(),
            'test_results': results,
            'summary': {
                'total_tests': len(results),
                'passed_tests': sum(results.values()),
                'success_rate': sum(results.values()) / len(results)
            }
        }
        
        results_file = RAW_DATA_DIR / f"imdb_scraping_test_{timestamp}.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Test results saved to: {results_file}")

def main():
    """Main execution function"""
    scraper = IMDBScraper()
    scraper.run_comprehensive_test()

if __name__ == "__main__":
    main()
