"""
Aspect-Based Sentiment Analysis Module
Performs sentiment analysis on extracted movie aspects
"""

import pandas as pd
import numpy as np
import json
import logging
from typing import List, Dict, Optional, Tuple
from pathlib import Path
import sys
from textblob import TextBlob
from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
import re
from collections import defaultdict

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from config import RESULTS_DIR, MOVIE_ASPECTS
from 04_sentiment_analysis.01_aspect_extraction import AspectExtractor

logger = logging.getLogger(__name__)

class AspectSentimentAnalyzer:
    """Comprehensive aspect-based sentiment analysis"""
    
    def __init__(self):
        self.aspect_extractor = AspectExtractor()
        self.vader_analyzer = SentimentIntensityAnalyzer()
        self.sentiment_results = []
        
        # Sentiment thresholds
        self.sentiment_thresholds = {
            'positive': 0.1,
            'negative': -0.1
        }
    
    def analyze_text_sentiment(self, text: str) -> Dict:
        """Analyze sentiment of text using multiple methods"""
        if not text:
            return {'error': 'Empty text'}
        
        sentiment_scores = {}
        
        # VADER sentiment
        try:
            vader_scores = self.vader_analyzer.polarity_scores(text)
            sentiment_scores['vader'] = {
                'compound': vader_scores['compound'],
                'positive': vader_scores['pos'],
                'negative': vader_scores['neg'],
                'neutral': vader_scores['neu'],
                'label': self._get_vader_label(vader_scores['compound'])
            }
        except Exception as e:
            logger.warning(f"Error in VADER analysis: {str(e)}")
        
        # TextBlob sentiment
        try:
            blob = TextBlob(text)
            sentiment_scores['textblob'] = {
                'polarity': blob.sentiment.polarity,
                'subjectivity': blob.sentiment.subjectivity,
                'label': self._get_textblob_label(blob.sentiment.polarity)
            }
        except Exception as e:
            logger.warning(f"Error in TextBlob analysis: {str(e)}")
        
        # Combined sentiment
        sentiment_scores['combined'] = self._combine_sentiments(sentiment_scores)
        
        return sentiment_scores
    
    def _get_vader_label(self, compound_score: float) -> str:
        """Get sentiment label from VADER compound score"""
        if compound_score >= self.sentiment_thresholds['positive']:
            return 'positive'
        elif compound_score <= self.sentiment_thresholds['negative']:
            return 'negative'
        else:
            return 'neutral'
    
    def _get_textblob_label(self, polarity: float) -> str:
        """Get sentiment label from TextBlob polarity"""
        if polarity > self.sentiment_thresholds['positive']:
            return 'positive'
        elif polarity < self.sentiment_thresholds['negative']:
            return 'negative'
        else:
            return 'neutral'
    
    def _combine_sentiments(self, sentiment_scores: Dict) -> Dict:
        """Combine sentiment scores from different methods"""
        combined = {'label': 'neutral', 'confidence': 0.0, 'score': 0.0}
        
        try:
            scores = []
            
            # Collect scores
            if 'vader' in sentiment_scores:
                scores.append(sentiment_scores['vader']['compound'])
            
            if 'textblob' in sentiment_scores:
                scores.append(sentiment_scores['textblob']['polarity'])
            
            if scores:
                # Average the scores
                avg_score = sum(scores) / len(scores)
                combined['score'] = avg_score
                
                # Determine label
                if avg_score >= self.sentiment_thresholds['positive']:
                    combined['label'] = 'positive'
                elif avg_score <= self.sentiment_thresholds['negative']:
                    combined['label'] = 'negative'
                else:
                    combined['label'] = 'neutral'
                
                # Calculate confidence based on agreement
                labels = []
                if 'vader' in sentiment_scores:
                    labels.append(sentiment_scores['vader']['label'])
                if 'textblob' in sentiment_scores:
                    labels.append(sentiment_scores['textblob']['label'])
                
                if len(set(labels)) == 1:  # All methods agree
                    combined['confidence'] = 1.0
                else:
                    combined['confidence'] = 0.5
        
        except Exception as e:
            logger.warning(f"Error combining sentiments: {str(e)}")
        
        return combined
    
    def analyze_aspect_sentiment(self, review: Dict) -> Dict:
        """Analyze sentiment for each aspect in a review"""
        aspect_sentiments = {}
        
        # Get extracted aspects
        extracted_aspects = review.get('extracted_aspects', {})
        
        for aspect, sentences in extracted_aspects.items():
            if not sentences:
                continue
            
            # Analyze sentiment for each sentence mentioning the aspect
            sentence_sentiments = []
            for sentence in sentences:
                sentiment = self.analyze_text_sentiment(sentence)
                sentence_sentiments.append(sentiment)
            
            # Aggregate sentiments for this aspect
            aspect_sentiment = self._aggregate_aspect_sentiments(sentence_sentiments)
            aspect_sentiments[aspect] = aspect_sentiment
        
        # Overall review sentiment
        review_text = review.get('text', '') or review.get('processed_text', '')
        overall_sentiment = self.analyze_text_sentiment(review_text)
        
        return {
            'aspect_sentiments': aspect_sentiments,
            'overall_sentiment': overall_sentiment,
            'review_id': review.get('id', ''),
            'movie_id': review.get('imdb_id') or review.get('tmdb_id', ''),
            'aspects_analyzed': list(aspect_sentiments.keys())
        }
    
    def _aggregate_aspect_sentiments(self, sentence_sentiments: List[Dict]) -> Dict:
        """Aggregate sentiment scores for multiple sentences about an aspect"""
        if not sentence_sentiments:
            return {'error': 'No sentiments to aggregate'}
        
        # Collect scores from combined sentiment
        scores = []
        labels = []
        confidences = []
        
        for sentiment in sentence_sentiments:
            combined = sentiment.get('combined', {})
            if 'score' in combined:
                scores.append(combined['score'])
                labels.append(combined.get('label', 'neutral'))
                confidences.append(combined.get('confidence', 0.0))
        
        if not scores:
            return {'error': 'No valid scores found'}
        
        # Calculate aggregated values
        avg_score = sum(scores) / len(scores)
        avg_confidence = sum(confidences) / len(confidences)
        
        # Determine final label
        label_counts = {}
        for label in labels:
            label_counts[label] = label_counts.get(label, 0) + 1
        
        final_label = max(label_counts.items(), key=lambda x: x[1])[0]
        
        return {
            'score': avg_score,
            'label': final_label,
            'confidence': avg_confidence,
            'sentence_count': len(sentence_sentiments),
            'label_distribution': label_counts
        }
    
    def analyze_aspects(self, reviews_data: List[Dict]) -> List[Dict]:
        """Perform aspect-based sentiment analysis on all reviews"""
        logger.info(f"Performing aspect-based sentiment analysis on {len(reviews_data)} reviews")
        
        # First extract aspects if not already done
        if not reviews_data or 'extracted_aspects' not in reviews_data[0]:
            logger.info("Extracting aspects first...")
            reviews_data = self.aspect_extractor.extract_aspects_from_reviews(reviews_data)
        
        sentiment_results = []
        
        for i, review in enumerate(reviews_data):
            if i % 50 == 0:
                logger.info(f"Analyzing sentiment for review {i+1}/{len(reviews_data)}")
            
            try:
                sentiment_result = self.analyze_aspect_sentiment(review)
                sentiment_results.append(sentiment_result)
                
            except Exception as e:
                logger.warning(f"Error analyzing sentiment for review {i}: {str(e)}")
                continue
        
        self.sentiment_results = sentiment_results
        
        # Save results
        self.save_sentiment_results(sentiment_results)
        
        logger.info(f"Aspect-based sentiment analysis completed for {len(sentiment_results)} reviews")
        return sentiment_results
    
    def generate_aspect_sentiment_summary(self, sentiment_results: List[Dict]) -> Dict:
        """Generate summary statistics for aspect sentiments"""
        logger.info("Generating aspect sentiment summary")
        
        aspect_stats = defaultdict(lambda: {
            'positive': 0, 'negative': 0, 'neutral': 0,
            'total': 0, 'avg_score': 0.0, 'scores': []
        })
        
        overall_stats = {
            'positive': 0, 'negative': 0, 'neutral': 0,
            'total': 0, 'avg_score': 0.0, 'scores': []
        }
        
        for result in sentiment_results:
            # Process aspect sentiments
            aspect_sentiments = result.get('aspect_sentiments', {})
            for aspect, sentiment in aspect_sentiments.items():
                if 'label' in sentiment and 'score' in sentiment:
                    aspect_stats[aspect]['total'] += 1
                    aspect_stats[aspect][sentiment['label']] += 1
                    aspect_stats[aspect]['scores'].append(sentiment['score'])
            
            # Process overall sentiment
            overall_sentiment = result.get('overall_sentiment', {}).get('combined', {})
            if 'label' in overall_sentiment and 'score' in overall_sentiment:
                overall_stats['total'] += 1
                overall_stats[overall_sentiment['label']] += 1
                overall_stats['scores'].append(overall_sentiment['score'])
        
        # Calculate averages and percentages
        summary = {
            'aspect_statistics': {},
            'overall_statistics': {},
            'aspect_rankings': {}
        }
        
        # Aspect statistics
        for aspect, stats in aspect_stats.items():
            if stats['total'] > 0:
                summary['aspect_statistics'][aspect] = {
                    'total_mentions': stats['total'],
                    'positive_count': stats['positive'],
                    'negative_count': stats['negative'],
                    'neutral_count': stats['neutral'],
                    'positive_percentage': (stats['positive'] / stats['total']) * 100,
                    'negative_percentage': (stats['negative'] / stats['total']) * 100,
                    'neutral_percentage': (stats['neutral'] / stats['total']) * 100,
                    'average_score': sum(stats['scores']) / len(stats['scores']) if stats['scores'] else 0
                }
        
        # Overall statistics
        if overall_stats['total'] > 0:
            summary['overall_statistics'] = {
                'total_reviews': overall_stats['total'],
                'positive_count': overall_stats['positive'],
                'negative_count': overall_stats['negative'],
                'neutral_count': overall_stats['neutral'],
                'positive_percentage': (overall_stats['positive'] / overall_stats['total']) * 100,
                'negative_percentage': (overall_stats['negative'] / overall_stats['total']) * 100,
                'neutral_percentage': (overall_stats['neutral'] / overall_stats['total']) * 100,
                'average_score': sum(overall_stats['scores']) / len(overall_stats['scores']) if overall_stats['scores'] else 0
            }
        
        # Aspect rankings
        aspect_scores = {}
        for aspect, stats in summary['aspect_statistics'].items():
            aspect_scores[aspect] = stats['average_score']
        
        summary['aspect_rankings'] = {
            'best_aspects': sorted(aspect_scores.items(), key=lambda x: x[1], reverse=True)[:5],
            'worst_aspects': sorted(aspect_scores.items(), key=lambda x: x[1])[:5]
        }
        
        return summary
    
    def generate_reports(self, sentiment_results: List[Dict], movies_data: List[Dict]) -> Dict:
        """Generate comprehensive sentiment analysis reports"""
        logger.info("Generating sentiment analysis reports")
        
        # Generate summary
        summary = self.generate_aspect_sentiment_summary(sentiment_results)
        
        # Movie-level analysis
        movie_sentiments = self._analyze_movie_level_sentiments(sentiment_results, movies_data)
        
        # Aspect comparison
        aspect_comparison = self._compare_aspects(sentiment_results)
        
        reports = {
            'summary': summary,
            'movie_sentiments': movie_sentiments,
            'aspect_comparison': aspect_comparison,
            'analysis_metadata': {
                'total_reviews_analyzed': len(sentiment_results),
                'total_movies': len(movies_data),
                'aspects_analyzed': MOVIE_ASPECTS
            }
        }
        
        # Save reports
        self.save_reports(reports)
        
        return reports
    
    def _analyze_movie_level_sentiments(self, sentiment_results: List[Dict], movies_data: List[Dict]) -> Dict:
        """Analyze sentiments at movie level"""
        movie_sentiments = defaultdict(lambda: {
            'reviews': [], 'aspect_scores': defaultdict(list),
            'overall_scores': []
        })
        
        # Group by movie
        for result in sentiment_results:
            movie_id = result.get('movie_id', 'unknown')
            movie_sentiments[movie_id]['reviews'].append(result)
            
            # Collect aspect scores
            for aspect, sentiment in result.get('aspect_sentiments', {}).items():
                if 'score' in sentiment:
                    movie_sentiments[movie_id]['aspect_scores'][aspect].append(sentiment['score'])
            
            # Collect overall score
            overall = result.get('overall_sentiment', {}).get('combined', {})
            if 'score' in overall:
                movie_sentiments[movie_id]['overall_scores'].append(overall['score'])
        
        # Calculate movie-level statistics
        movie_stats = {}
        for movie_id, data in movie_sentiments.items():
            stats = {
                'review_count': len(data['reviews']),
                'overall_sentiment': {
                    'average_score': sum(data['overall_scores']) / len(data['overall_scores']) if data['overall_scores'] else 0,
                    'score_range': [min(data['overall_scores']), max(data['overall_scores'])] if data['overall_scores'] else [0, 0]
                },
                'aspect_sentiments': {}
            }
            
            for aspect, scores in data['aspect_scores'].items():
                if scores:
                    stats['aspect_sentiments'][aspect] = {
                        'average_score': sum(scores) / len(scores),
                        'mention_count': len(scores),
                        'score_range': [min(scores), max(scores)]
                    }
            
            movie_stats[movie_id] = stats
        
        return movie_stats
    
    def _compare_aspects(self, sentiment_results: List[Dict]) -> Dict:
        """Compare sentiment across different aspects"""
        aspect_comparisons = {}
        
        # Collect all aspect scores
        aspect_scores = defaultdict(list)
        for result in sentiment_results:
            for aspect, sentiment in result.get('aspect_sentiments', {}).items():
                if 'score' in sentiment:
                    aspect_scores[aspect].append(sentiment['score'])
        
        # Calculate comparison metrics
        for aspect, scores in aspect_scores.items():
            if scores:
                aspect_comparisons[aspect] = {
                    'mean_score': np.mean(scores),
                    'median_score': np.median(scores),
                    'std_score': np.std(scores),
                    'min_score': min(scores),
                    'max_score': max(scores),
                    'sample_size': len(scores)
                }
        
        return aspect_comparisons
    
    def save_sentiment_results(self, sentiment_results: List[Dict], filename: str = 'aspect_sentiment_results.json'):
        """Save sentiment analysis results"""
        filepath = RESULTS_DIR / filename
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(sentiment_results, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Sentiment results saved to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving sentiment results: {str(e)}")
    
    def save_reports(self, reports: Dict, filename: str = 'sentiment_analysis_reports.json'):
        """Save comprehensive reports"""
        filepath = RESULTS_DIR / filename
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(reports, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Sentiment reports saved to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving sentiment reports: {str(e)}")

if __name__ == "__main__":
    # Configure logging for standalone execution
    logging.basicConfig(level=logging.INFO)
    
    analyzer = AspectSentimentAnalyzer()
    
    # Example usage
    sample_review = {
        'text': 'The acting was excellent but the plot was confusing. Great cinematography though!',
        'extracted_aspects': {
            'acting': ['The acting was excellent'],
            'plot': ['the plot was confusing'],
            'cinematography': ['Great cinematography']
        }
    }
    
    result = analyzer.analyze_aspect_sentiment(sample_review)
    print(f"Aspect sentiment analysis result: {json.dumps(result, indent=2)}")
