# Environment Variables for IMDB Aspect-Based Sentiment Analysis

# TMDB API Configuration
TMDB_API_KEY=your_tmdb_api_key_here

# Database Configuration (if using external database)
DATABASE_URL=sqlite:///imdb_sentiment.db

# Logging Configuration
LOG_LEVEL=INFO

# API Rate Limiting
REQUEST_DELAY=1
MAX_RETRIES=3

# Data Collection Settings
TARGET_YEAR=2025
MAX_MOVIES_PER_GENRE=50
MAX_REVIEWS_PER_MOVIE=100
MIN_REVIEW_LENGTH=50

# Text Processing Settings
STOP_WORDS_REMOVE=True
LEMMATIZATION=True
MIN_WORD_LENGTH=2

# Model Configuration
BERT_MODEL_NAME=bert-base-uncased
MAX_SEQUENCE_LENGTH=512
BATCH_SIZE=16
LEARNING_RATE=2e-5
EPOCHS=3

# Dashboard Configuration
STREAMLIT_PORT=8501

# File Paths (relative to project root)
RAW_DATA_DIR=data/raw
PROCESSED_DATA_DIR=data/processed
RESULTS_DIR=data/results
MODELS_DIR=models
LOGS_DIR=logs
