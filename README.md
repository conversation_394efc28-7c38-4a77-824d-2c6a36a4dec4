# IMDB Aspect-Based Sentiment Analysis Project

## Project Overview
This project implements aspect-based sentiment analysis on IMDB movie reviews for 2025 movies. The analysis focuses on different aspects of movies such as plot, acting, direction, cinematography, and overall sentiment.

## Project Structure
```
python-absa-imdb/
├── 01_data_collection/
│   ├── 01_imdb_scraper.py          # IMDB movie data scraping
│   ├── 02_tmdb_scraper.py          # TMDB API integration
│   ├── 03_review_scraper.py        # Movie reviews collection
│   └── config/
│       └── scraping_config.py      # Scraping configuration
├── 02_data_cleansing/
│   ├── 01_text_preprocessing.py    # Text cleaning and preprocessing
│   ├── 02_data_validation.py       # Data quality validation
│   ├── 03_duplicate_removal.py     # Remove duplicate reviews
│   └── utils/
│       └── cleaning_utils.py       # Utility functions for cleaning
├── 03_eda_analysis/
│   ├── 01_descriptive_stats.py     # Basic statistical analysis
│   ├── 02_data_visualization.py    # Data visualization
│   ├── 03_correlation_analysis.py  # Correlation analysis
│   └── 04_text_analysis.py         # Text-specific EDA
├── 04_sentiment_analysis/
│   ├── 01_aspect_extraction.py     # Extract movie aspects
│   ├── 02_sentiment_models.py      # Sentiment analysis models
│   ├── 03_aspect_sentiment.py      # Aspect-based sentiment analysis
│   └── models/
│       ├── bert_model.py           # BERT-based model
│       └── traditional_models.py   # Traditional ML models
├── 05_visualization/
│   ├── 01_sentiment_plots.py       # Sentiment visualization
│   ├── 02_aspect_analysis_plots.py # Aspect analysis plots
│   ├── 03_interactive_dashboard.py # Interactive dashboard
│   └── templates/
│       └── dashboard.html          # Dashboard template
├── 06_results/
│   ├── 01_model_evaluation.py      # Model performance evaluation
│   ├── 02_results_analysis.py      # Results interpretation
│   └── 03_report_generation.py     # Generate final reports
├── data/
│   ├── raw/                        # Raw scraped data
│   ├── processed/                  # Cleaned and processed data
│   └── results/                    # Analysis results
├── notebooks/
│   ├── 01_data_exploration.ipynb   # Initial data exploration
│   ├── 02_model_development.ipynb  # Model development
│   └── 03_results_analysis.ipynb   # Results analysis
├── tests/
│   └── test_modules.py             # Unit tests
├── requirements.txt                # Project dependencies
├── config.py                       # Global configuration
└── main.py                         # Main execution script
```

## Features
- **Data Collection**: Scrape IMDB and TMDB for 2025 movies data
- **Data Cleansing**: Comprehensive text preprocessing and data validation
- **EDA**: Statistical analysis and visualization of movie data
- **Aspect-Based Sentiment Analysis**: Analyze sentiment for different movie aspects
- **Visualization**: Interactive dashboards and comprehensive plots
- **Model Evaluation**: Performance metrics and model comparison

## Installation
```bash
pip install -r requirements.txt
```

## Usage
```bash
python main.py
```

## Dependencies
- pandas, numpy: Data manipulation
- requests, beautifulsoup4: Web scraping
- scikit-learn: Machine learning
- transformers: BERT models
- matplotlib, seaborn, plotly: Visualization
- streamlit: Interactive dashboard
- nltk, spacy: Natural language processing

## License
MIT License
