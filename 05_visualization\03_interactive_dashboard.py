"""
Interactive Dashboard Module
Creates Streamlit dashboard for IMDB aspect-based sentiment analysis
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
import logging
from pathlib import Path
import sys
from typing import List, Dict, Optional
from wordcloud import WordCloud
import matplotlib.pyplot as plt

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from config import RESULTS_DIR, PROCESSED_DATA_DIR, DASHBOARD_TITLE

logger = logging.getLogger(__name__)

class SentimentDashboard:
    """Interactive dashboard for sentiment analysis results"""
    
    def __init__(self):
        self.data_loaded = False
        self.movies_data = []
        self.reviews_data = []
        self.sentiment_results = []
        self.reports = {}
    
    def load_data(self):
        """Load all necessary data for dashboard"""
        try:
            # Load movies data
            movies_file = PROCESSED_DATA_DIR / 'cleaned_movies.json'
            if movies_file.exists():
                with open(movies_file, 'r', encoding='utf-8') as f:
                    self.movies_data = json.load(f)
            
            # Load reviews data
            reviews_file = PROCESSED_DATA_DIR / 'processed_reviews.json'
            if reviews_file.exists():
                with open(reviews_file, 'r', encoding='utf-8') as f:
                    self.reviews_data = json.load(f)
            
            # Load sentiment results
            sentiment_file = RESULTS_DIR / 'aspect_sentiment_results.json'
            if sentiment_file.exists():
                with open(sentiment_file, 'r', encoding='utf-8') as f:
                    self.sentiment_results = json.load(f)
            
            # Load reports
            reports_file = RESULTS_DIR / 'sentiment_analysis_reports.json'
            if reports_file.exists():
                with open(reports_file, 'r', encoding='utf-8') as f:
                    self.reports = json.load(f)
            
            self.data_loaded = True
            return True
            
        except Exception as e:
            st.error(f"Error loading data: {str(e)}")
            return False
    
    def create_overview_page(self):
        """Create overview page"""
        st.title("📊 IMDB Aspect-Based Sentiment Analysis")
        st.markdown("---")
        
        if not self.data_loaded:
            st.warning("Data not loaded. Please run the analysis pipeline first.")
            return
        
        # Key metrics
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("Total Movies", len(self.movies_data))
        
        with col2:
            st.metric("Total Reviews", len(self.reviews_data))
        
        with col3:
            total_analyzed = len(self.sentiment_results)
            st.metric("Reviews Analyzed", total_analyzed)
        
        with col4:
            if self.reports.get('summary', {}).get('overall_statistics'):
                avg_sentiment = self.reports['summary']['overall_statistics'].get('average_score', 0)
                st.metric("Avg Sentiment", f"{avg_sentiment:.2f}")
        
        # Overall sentiment distribution
        if self.reports.get('summary', {}).get('overall_statistics'):
            st.subheader("Overall Sentiment Distribution")
            
            overall_stats = self.reports['summary']['overall_statistics']
            
            fig = go.Figure(data=[
                go.Bar(
                    x=['Positive', 'Neutral', 'Negative'],
                    y=[
                        overall_stats.get('positive_percentage', 0),
                        overall_stats.get('neutral_percentage', 0),
                        overall_stats.get('negative_percentage', 0)
                    ],
                    marker_color=['green', 'gray', 'red']
                )
            ])
            
            fig.update_layout(
                title="Sentiment Distribution (%)",
                xaxis_title="Sentiment",
                yaxis_title="Percentage"
            )
            
            st.plotly_chart(fig, use_container_width=True)
    
    def create_aspect_analysis_page(self):
        """Create aspect analysis page"""
        st.title("🎬 Aspect-Based Analysis")
        st.markdown("---")
        
        if not self.data_loaded or not self.reports.get('summary', {}).get('aspect_statistics'):
            st.warning("Aspect analysis data not available.")
            return
        
        aspect_stats = self.reports['summary']['aspect_statistics']
        
        # Aspect selection
        aspects = list(aspect_stats.keys())
        selected_aspects = st.multiselect(
            "Select aspects to analyze:",
            aspects,
            default=aspects[:5] if len(aspects) > 5 else aspects
        )
        
        if not selected_aspects:
            st.warning("Please select at least one aspect.")
            return
        
        # Aspect sentiment comparison
        st.subheader("Aspect Sentiment Comparison")
        
        aspect_scores = []
        aspect_names = []
        positive_pcts = []
        negative_pcts = []
        
        for aspect in selected_aspects:
            stats = aspect_stats[aspect]
            aspect_names.append(aspect.title())
            aspect_scores.append(stats['average_score'])
            positive_pcts.append(stats['positive_percentage'])
            negative_pcts.append(stats['negative_percentage'])
        
        # Create subplot
        fig = make_subplots(
            rows=1, cols=2,
            subplot_titles=('Average Sentiment Score', 'Positive vs Negative %'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        # Average scores
        fig.add_trace(
            go.Bar(x=aspect_names, y=aspect_scores, name="Avg Score", marker_color='blue'),
            row=1, col=1
        )
        
        # Positive vs Negative
        fig.add_trace(
            go.Bar(x=aspect_names, y=positive_pcts, name="Positive %", marker_color='green'),
            row=1, col=2
        )
        fig.add_trace(
            go.Bar(x=aspect_names, y=negative_pcts, name="Negative %", marker_color='red'),
            row=1, col=2
        )
        
        fig.update_layout(height=500, showlegend=True)
        st.plotly_chart(fig, use_container_width=True)
        
        # Detailed aspect statistics
        st.subheader("Detailed Aspect Statistics")
        
        aspect_df = pd.DataFrame([
            {
                'Aspect': aspect.title(),
                'Total Mentions': stats['total_mentions'],
                'Avg Score': f"{stats['average_score']:.2f}",
                'Positive %': f"{stats['positive_percentage']:.1f}%",
                'Negative %': f"{stats['negative_percentage']:.1f}%",
                'Neutral %': f"{stats['neutral_percentage']:.1f}%"
            }
            for aspect, stats in aspect_stats.items()
            if aspect in selected_aspects
        ])
        
        st.dataframe(aspect_df, use_container_width=True)
    
    def create_movie_analysis_page(self):
        """Create movie-level analysis page"""
        st.title("🎭 Movie Analysis")
        st.markdown("---")
        
        if not self.data_loaded:
            st.warning("Movie analysis data not available.")
            return
        
        # Movie selection
        movie_options = {}
        for movie in self.movies_data:
            movie_id = movie.get('imdb_id') or movie.get('tmdb_id', 'unknown')
            title = movie.get('title', 'Unknown Title')
            movie_options[f"{title} ({movie_id})"] = movie_id
        
        if not movie_options:
            st.warning("No movie data available.")
            return
        
        selected_movie_display = st.selectbox(
            "Select a movie to analyze:",
            list(movie_options.keys())
        )
        
        selected_movie_id = movie_options[selected_movie_display]
        
        # Get movie data
        movie_data = next((m for m in self.movies_data 
                          if (m.get('imdb_id') == selected_movie_id or 
                              m.get('tmdb_id') == selected_movie_id)), None)
        
        if not movie_data:
            st.error("Movie data not found.")
            return
        
        # Movie information
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("Movie Information")
            st.write(f"**Title:** {movie_data.get('title', 'N/A')}")
            st.write(f"**Year:** {movie_data.get('year', 'N/A')}")
            st.write(f"**Rating:** {movie_data.get('rating', 'N/A')}")
            st.write(f"**Genres:** {', '.join(movie_data.get('genres', []))}")
        
        with col2:
            if movie_data.get('overview') or movie_data.get('plot'):
                st.subheader("Plot")
                plot_text = movie_data.get('overview') or movie_data.get('plot')
                st.write(plot_text[:300] + "..." if len(plot_text) > 300 else plot_text)
        
        # Movie sentiment analysis
        if self.reports.get('movie_sentiments', {}).get(selected_movie_id):
            movie_sentiment = self.reports['movie_sentiments'][selected_movie_id]
            
            st.subheader("Sentiment Analysis")
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric("Review Count", movie_sentiment['review_count'])
            
            with col2:
                overall_score = movie_sentiment['overall_sentiment']['average_score']
                st.metric("Overall Sentiment", f"{overall_score:.2f}")
            
            with col3:
                aspect_count = len(movie_sentiment['aspect_sentiments'])
                st.metric("Aspects Mentioned", aspect_count)
            
            # Aspect sentiments for this movie
            if movie_sentiment['aspect_sentiments']:
                st.subheader("Aspect Sentiments")
                
                aspects = list(movie_sentiment['aspect_sentiments'].keys())
                scores = [movie_sentiment['aspect_sentiments'][aspect]['average_score'] 
                         for aspect in aspects]
                
                fig = go.Figure(data=[
                    go.Bar(x=[a.title() for a in aspects], y=scores, 
                          marker_color=['green' if s > 0 else 'red' if s < 0 else 'gray' for s in scores])
                ])
                
                fig.update_layout(
                    title="Aspect Sentiment Scores",
                    xaxis_title="Aspect",
                    yaxis_title="Average Score"
                )
                
                st.plotly_chart(fig, use_container_width=True)
    
    def create_data_explorer_page(self):
        """Create data explorer page"""
        st.title("🔍 Data Explorer")
        st.markdown("---")
        
        if not self.data_loaded:
            st.warning("Data not available.")
            return
        
        # Data selection
        data_type = st.selectbox(
            "Select data to explore:",
            ["Movies", "Reviews", "Sentiment Results"]
        )
        
        if data_type == "Movies" and self.movies_data:
            st.subheader("Movies Data")
            df = pd.DataFrame(self.movies_data)
            st.dataframe(df, use_container_width=True)
            
            # Download button
            csv = df.to_csv(index=False)
            st.download_button(
                label="Download Movies CSV",
                data=csv,
                file_name="movies_data.csv",
                mime="text/csv"
            )
        
        elif data_type == "Reviews" and self.reviews_data:
            st.subheader("Reviews Data")
            df = pd.DataFrame(self.reviews_data)
            
            # Show sample
            st.write(f"Showing first 100 reviews out of {len(df)} total reviews")
            st.dataframe(df.head(100), use_container_width=True)
            
            # Download button
            csv = df.to_csv(index=False)
            st.download_button(
                label="Download Reviews CSV",
                data=csv,
                file_name="reviews_data.csv",
                mime="text/csv"
            )
        
        elif data_type == "Sentiment Results" and self.sentiment_results:
            st.subheader("Sentiment Analysis Results")
            df = pd.DataFrame(self.sentiment_results)
            
            # Show sample
            st.write(f"Showing first 100 results out of {len(df)} total results")
            st.dataframe(df.head(100), use_container_width=True)
            
            # Download button
            csv = df.to_csv(index=False)
            st.download_button(
                label="Download Sentiment Results CSV",
                data=csv,
                file_name="sentiment_results.csv",
                mime="text/csv"
            )

def create_dashboard():
    """Create and run the Streamlit dashboard"""
    st.set_page_config(
        page_title=DASHBOARD_TITLE,
        page_icon="🎬",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Initialize dashboard
    dashboard = SentimentDashboard()
    
    # Sidebar navigation
    st.sidebar.title("Navigation")
    page = st.sidebar.selectbox(
        "Choose a page:",
        ["Overview", "Aspect Analysis", "Movie Analysis", "Data Explorer"]
    )
    
    # Load data
    if st.sidebar.button("Reload Data"):
        with st.spinner("Loading data..."):
            dashboard.load_data()
        st.sidebar.success("Data loaded!")
    else:
        dashboard.load_data()
    
    # Display selected page
    if page == "Overview":
        dashboard.create_overview_page()
    elif page == "Aspect Analysis":
        dashboard.create_aspect_analysis_page()
    elif page == "Movie Analysis":
        dashboard.create_movie_analysis_page()
    elif page == "Data Explorer":
        dashboard.create_data_explorer_page()
    
    # Sidebar info
    st.sidebar.markdown("---")
    st.sidebar.markdown("### About")
    st.sidebar.markdown(
        "This dashboard provides interactive analysis of IMDB movie reviews "
        "using aspect-based sentiment analysis."
    )
    
    if dashboard.data_loaded:
        st.sidebar.markdown(f"**Movies:** {len(dashboard.movies_data)}")
        st.sidebar.markdown(f"**Reviews:** {len(dashboard.reviews_data)}")
        st.sidebar.markdown(f"**Analyzed:** {len(dashboard.sentiment_results)}")

def launch_dashboard():
    """Launch the Streamlit dashboard"""
    try:
        import subprocess
        import sys
        
        # Get the path to this file
        dashboard_file = Path(__file__)
        
        # Launch streamlit
        cmd = [sys.executable, "-m", "streamlit", "run", str(dashboard_file)]
        subprocess.run(cmd)
        
    except Exception as e:
        logger.error(f"Error launching dashboard: {str(e)}")
        print(f"Error launching dashboard: {str(e)}")
        print("Please run manually: streamlit run 05_visualization/03_interactive_dashboard.py")

if __name__ == "__main__":
    create_dashboard()
