"""
IMDB Movie Data Scraper
Scrapes movie information from IMDB for 2025 movies
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import time
import logging
from typing import List, Dict, Optional
import re
from urllib.parse import urljoin, urlparse
import json
from pathlib import Path
import sys

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from config import RAW_DATA_DIR, TARGET_YEAR
from 01_data_collection.config.scraping_config import IMDB_CONFIG, SCRAPING_PARAMS, IMDB_SELECTORS

logger = logging.getLogger(__name__)

class IMDBScraper:
    """Scraper for IMDB movie data"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update(IMDB_CONFIG['headers'])
        self.base_url = IMDB_CONFIG['base_url']
        self.movies_data = []
        
    def search_movies_by_year(self, year: int = TARGET_YEAR) -> List[Dict]:
        """Search for movies by release year"""
        logger.info(f"Searching for movies released in {year}")
        
        search_params = {
            'title_type': 'feature',
            'release_date': f'{year}-01-01,{year}-12-31',
            'sort': 'popularity,asc',
            'count': 250
        }
        
        try:
            response = self.session.get(
                IMDB_CONFIG['search_url'],
                params=search_params,
                timeout=IMDB_CONFIG['timeout']
            )
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            movie_containers = soup.find_all('div', class_='lister-item')
            
            movies = []
            for container in movie_containers:
                movie_data = self._extract_movie_basic_info(container)
                if movie_data:
                    movies.append(movie_data)
                    
            logger.info(f"Found {len(movies)} movies for year {year}")
            return movies
            
        except Exception as e:
            logger.error(f"Error searching movies for year {year}: {str(e)}")
            return []
    
    def _extract_movie_basic_info(self, container) -> Optional[Dict]:
        """Extract basic movie information from search results"""
        try:
            # Extract IMDB ID
            title_link = container.find('h3', class_='lister-item-header').find('a')
            if not title_link:
                return None
                
            href = title_link.get('href', '')
            imdb_id_match = re.search(r'/title/(tt\d+)/', href)
            if not imdb_id_match:
                return None
                
            imdb_id = imdb_id_match.group(1)
            
            # Extract basic info
            title = title_link.text.strip()
            year_span = container.find('span', class_='lister-item-year')
            year = re.search(r'\((\d{4})\)', year_span.text).group(1) if year_span else None
            
            # Extract rating
            rating_span = container.find('div', class_='ratings-bar')
            rating = None
            if rating_span:
                rating_strong = rating_span.find('strong')
                if rating_strong:
                    rating = float(rating_strong.text)
            
            # Extract genre
            genre_span = container.find('span', class_='genre')
            genres = [g.strip() for g in genre_span.text.split(',')] if genre_span else []
            
            return {
                'imdb_id': imdb_id,
                'title': title,
                'year': int(year) if year else None,
                'rating': rating,
                'genres': genres,
                'imdb_url': urljoin(self.base_url, href)
            }
            
        except Exception as e:
            logger.warning(f"Error extracting movie info: {str(e)}")
            return None
    
    def get_movie_details(self, imdb_id: str) -> Optional[Dict]:
        """Get detailed information for a specific movie"""
        logger.debug(f"Getting details for movie {imdb_id}")
        
        movie_url = IMDB_CONFIG['movie_url_template'].format(imdb_id=imdb_id)
        
        try:
            response = self.session.get(movie_url, timeout=IMDB_CONFIG['timeout'])
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract detailed information
            details = {
                'imdb_id': imdb_id,
                'url': movie_url
            }
            
            # Title
            title_elem = soup.select_one(IMDB_SELECTORS['movie_title'])
            if title_elem:
                details['title'] = title_elem.text.strip()
            
            # Plot
            plot_elem = soup.select_one(IMDB_SELECTORS['movie_plot'])
            if plot_elem:
                details['plot'] = plot_elem.text.strip()
            
            # Rating
            rating_elem = soup.select_one(IMDB_SELECTORS['movie_rating'])
            if rating_elem:
                try:
                    details['imdb_rating'] = float(rating_elem.text.strip())
                except ValueError:
                    pass
            
            # Genres
            genre_elems = soup.select(IMDB_SELECTORS['movie_genres'])
            if genre_elems:
                details['genres'] = [elem.text.strip() for elem in genre_elems]
            
            # Director and cast would require more complex parsing
            # This is a simplified version
            
            time.sleep(IMDB_CONFIG['request_delay'])
            return details
            
        except Exception as e:
            logger.error(f"Error getting details for movie {imdb_id}: {str(e)}")
            return None
    
    def enrich_movie_data(self, movies_list: List[Dict]) -> List[Dict]:
        """Enrich basic movie data with detailed information"""
        logger.info(f"Enriching data for {len(movies_list)} movies")
        
        enriched_movies = []
        for i, movie in enumerate(movies_list):
            logger.info(f"Processing movie {i+1}/{len(movies_list)}: {movie.get('title', 'Unknown')}")
            
            if 'imdb_id' in movie:
                detailed_info = self.get_movie_details(movie['imdb_id'])
                if detailed_info:
                    # Merge basic and detailed info
                    enriched_movie = {**movie, **detailed_info}
                    enriched_movies.append(enriched_movie)
                else:
                    enriched_movies.append(movie)
            else:
                enriched_movies.append(movie)
        
        return enriched_movies
    
    def save_movies_data(self, movies_data: List[Dict], filename: str = 'imdb_movies_2025.json'):
        """Save movies data to file"""
        filepath = RAW_DATA_DIR / filename
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(movies_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Saved {len(movies_data)} movies to {filepath}")
            
            # Also save as CSV for easy viewing
            df = pd.DataFrame(movies_data)
            csv_filepath = filepath.with_suffix('.csv')
            df.to_csv(csv_filepath, index=False, encoding='utf-8')
            logger.info(f"Also saved as CSV: {csv_filepath}")
            
        except Exception as e:
            logger.error(f"Error saving movies data: {str(e)}")
    
    def run_full_scraping(self) -> List[Dict]:
        """Run complete IMDB scraping process"""
        logger.info("Starting full IMDB scraping process")
        
        # Search for movies
        movies = self.search_movies_by_year(TARGET_YEAR)
        
        if not movies:
            logger.warning("No movies found")
            return []
        
        # Enrich with detailed data
        enriched_movies = self.enrich_movie_data(movies)
        
        # Save data
        self.save_movies_data(enriched_movies)
        
        logger.info(f"IMDB scraping completed. Collected {len(enriched_movies)} movies")
        return enriched_movies

if __name__ == "__main__":
    # Configure logging for standalone execution
    logging.basicConfig(level=logging.INFO)
    
    scraper = IMDBScraper()
    movies_data = scraper.run_full_scraping()
    print(f"Scraped {len(movies_data)} movies from IMDB")
