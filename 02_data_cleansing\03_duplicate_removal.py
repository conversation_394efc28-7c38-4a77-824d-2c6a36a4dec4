"""
Duplicate Removal <PERSON>
Identifies and removes duplicate records from movie and review datasets
"""

import pandas as pd
import numpy as np
import json
import logging
from typing import List, Dict, Optional, Tuple, Set
from pathlib import Path
import sys
import hashlib
from difflib import SequenceMatcher

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from config import PROCESSED_DATA_DIR

logger = logging.getLogger(__name__)

class DuplicateRemover:
    """Advanced duplicate detection and removal"""
    
    def __init__(self, similarity_threshold: float = 0.85):
        self.similarity_threshold = similarity_threshold
        self.duplicate_report = {
            'movies': {},
            'reviews': {},
            'summary': {}
        }
    
    def remove_movie_duplicates(self, movies_data: List[Dict]) -> Tuple[List[Dict], Dict]:
        """Remove duplicate movies with detailed reporting"""
        logger.info(f"Removing duplicates from {len(movies_data)} movies")
        
        # Different strategies for duplicate detection
        exact_duplicates = self._find_exact_movie_duplicates(movies_data)
        fuzzy_duplicates = self._find_fuzzy_movie_duplicates(movies_data)
        
        # Combine duplicate sets
        all_duplicates = set()
        all_duplicates.update(exact_duplicates)
        all_duplicates.update(fuzzy_duplicates)
        
        # Remove duplicates
        unique_movies = []
        for i, movie in enumerate(movies_data):
            if i not in all_duplicates:
                unique_movies.append(movie)
        
        # Generate report
        duplicate_info = {
            'original_count': len(movies_data),
            'unique_count': len(unique_movies),
            'duplicates_removed': len(movies_data) - len(unique_movies),
            'exact_duplicates': len(exact_duplicates),
            'fuzzy_duplicates': len(fuzzy_duplicates),
            'duplicate_indices': list(all_duplicates)
        }
        
        self.duplicate_report['movies'] = duplicate_info
        
        logger.info(f"Removed {duplicate_info['duplicates_removed']} duplicate movies. "
                   f"{duplicate_info['unique_count']} unique movies remaining")
        
        return unique_movies, duplicate_info
    
    def _find_exact_movie_duplicates(self, movies_data: List[Dict]) -> Set[int]:
        """Find exact duplicate movies based on key fields"""
        seen_movies = {}
        duplicates = set()
        
        for i, movie in enumerate(movies_data):
            # Create key from title, year, and IDs
            title = str(movie.get('title', '')).lower().strip()
            year = movie.get('year')
            imdb_id = movie.get('imdb_id', '')
            tmdb_id = movie.get('tmdb_id', '')
            
            # Primary key: IMDB ID or TMDB ID
            if imdb_id:
                key = f"imdb_{imdb_id}"
            elif tmdb_id:
                key = f"tmdb_{tmdb_id}"
            else:
                # Fallback to title + year
                key = f"title_{title}_{year}"
            
            if key in seen_movies:
                duplicates.add(i)  # Mark current as duplicate
                logger.debug(f"Exact duplicate found: {title} ({year})")
            else:
                seen_movies[key] = i
        
        return duplicates
    
    def _find_fuzzy_movie_duplicates(self, movies_data: List[Dict]) -> Set[int]:
        """Find fuzzy duplicate movies based on title similarity"""
        duplicates = set()
        processed_indices = set()
        
        for i, movie1 in enumerate(movies_data):
            if i in processed_indices:
                continue
            
            title1 = str(movie1.get('title', '')).lower().strip()
            year1 = movie1.get('year')
            
            if not title1:
                continue
            
            for j, movie2 in enumerate(movies_data[i+1:], i+1):
                if j in processed_indices:
                    continue
                
                title2 = str(movie2.get('title', '')).lower().strip()
                year2 = movie2.get('year')
                
                if not title2:
                    continue
                
                # Check if titles are similar and years match (or are close)
                title_similarity = SequenceMatcher(None, title1, title2).ratio()
                year_match = (year1 == year2) or (year1 and year2 and abs(year1 - year2) <= 1)
                
                if title_similarity >= self.similarity_threshold and year_match:
                    duplicates.add(j)  # Mark second occurrence as duplicate
                    processed_indices.add(j)
                    logger.debug(f"Fuzzy duplicate found: '{title1}' vs '{title2}' "
                               f"(similarity: {title_similarity:.2f})")
        
        return duplicates
    
    def remove_review_duplicates(self, reviews_data: List[Dict]) -> Tuple[List[Dict], Dict]:
        """Remove duplicate reviews with detailed reporting"""
        logger.info(f"Removing duplicates from {len(reviews_data)} reviews")
        
        # Different strategies for duplicate detection
        exact_duplicates = self._find_exact_review_duplicates(reviews_data)
        content_duplicates = self._find_content_review_duplicates(reviews_data)
        
        # Combine duplicate sets
        all_duplicates = set()
        all_duplicates.update(exact_duplicates)
        all_duplicates.update(content_duplicates)
        
        # Remove duplicates
        unique_reviews = []
        for i, review in enumerate(reviews_data):
            if i not in all_duplicates:
                unique_reviews.append(review)
        
        # Generate report
        duplicate_info = {
            'original_count': len(reviews_data),
            'unique_count': len(unique_reviews),
            'duplicates_removed': len(reviews_data) - len(unique_reviews),
            'exact_duplicates': len(exact_duplicates),
            'content_duplicates': len(content_duplicates),
            'duplicate_indices': list(all_duplicates)
        }
        
        self.duplicate_report['reviews'] = duplicate_info
        
        logger.info(f"Removed {duplicate_info['duplicates_removed']} duplicate reviews. "
                   f"{duplicate_info['unique_count']} unique reviews remaining")
        
        return unique_reviews, duplicate_info
    
    def _find_exact_review_duplicates(self, reviews_data: List[Dict]) -> Set[int]:
        """Find exact duplicate reviews"""
        seen_reviews = {}
        duplicates = set()
        
        for i, review in enumerate(reviews_data):
            # Create hash from text and movie ID
            text = str(review.get('text', '')).strip()
            movie_id = (review.get('imdb_id') or 
                       review.get('tmdb_id') or 
                       review.get('movie_id', ''))
            
            # Create hash key
            content_hash = hashlib.md5(f"{text}_{movie_id}".encode()).hexdigest()
            
            if content_hash in seen_reviews:
                duplicates.add(i)
                logger.debug(f"Exact review duplicate found for movie {movie_id}")
            else:
                seen_reviews[content_hash] = i
        
        return duplicates
    
    def _find_content_review_duplicates(self, reviews_data: List[Dict]) -> Set[int]:
        """Find reviews with similar content"""
        duplicates = set()
        processed_indices = set()
        
        # Group reviews by movie for efficiency
        movie_reviews = {}
        for i, review in enumerate(reviews_data):
            movie_id = (review.get('imdb_id') or 
                       review.get('tmdb_id') or 
                       review.get('movie_id', 'unknown'))
            
            if movie_id not in movie_reviews:
                movie_reviews[movie_id] = []
            movie_reviews[movie_id].append((i, review))
        
        # Check for similar content within each movie
        for movie_id, reviews in movie_reviews.items():
            if len(reviews) < 2:
                continue
            
            for i, (idx1, review1) in enumerate(reviews):
                if idx1 in processed_indices:
                    continue
                
                text1 = str(review1.get('text', '')).lower().strip()
                if len(text1) < 50:  # Skip very short reviews
                    continue
                
                for j, (idx2, review2) in enumerate(reviews[i+1:], i+1):
                    if idx2 in processed_indices:
                        continue
                    
                    text2 = str(review2.get('text', '')).lower().strip()
                    if len(text2) < 50:
                        continue
                    
                    # Calculate similarity
                    similarity = self._calculate_text_similarity(text1, text2)
                    
                    if similarity >= self.similarity_threshold:
                        duplicates.add(idx2)  # Mark second occurrence as duplicate
                        processed_indices.add(idx2)
                        logger.debug(f"Content duplicate found for movie {movie_id} "
                                   f"(similarity: {similarity:.2f})")
        
        return duplicates
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two texts"""
        # Use sequence matcher for similarity
        return SequenceMatcher(None, text1, text2).ratio()
    
    def remove_all_duplicates(self, movies_data: List[Dict], 
                            reviews_data: List[Dict]) -> Tuple[List[Dict], List[Dict], Dict]:
        """Remove duplicates from both datasets"""
        logger.info("Starting comprehensive duplicate removal")
        
        # Remove movie duplicates
        unique_movies, movie_report = self.remove_movie_duplicates(movies_data)
        
        # Remove review duplicates
        unique_reviews, review_report = self.remove_review_duplicates(reviews_data)
        
        # Generate summary report
        summary_report = {
            'movies': movie_report,
            'reviews': review_report,
            'total_duplicates_removed': (movie_report['duplicates_removed'] + 
                                       review_report['duplicates_removed']),
            'processing_timestamp': pd.Timestamp.now().isoformat()
        }
        
        self.duplicate_report['summary'] = summary_report
        
        # Save report
        self.save_duplicate_report()
        
        logger.info(f"Duplicate removal completed. "
                   f"Movies: {movie_report['unique_count']}/{movie_report['original_count']}, "
                   f"Reviews: {review_report['unique_count']}/{review_report['original_count']}")
        
        return unique_movies, unique_reviews, summary_report
    
    def save_duplicate_report(self, filename: str = 'duplicate_removal_report.json'):
        """Save duplicate removal report"""
        filepath = PROCESSED_DATA_DIR / filename
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.duplicate_report, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Duplicate removal report saved to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving duplicate report: {str(e)}")
    
    def save_cleaned_data(self, movies_data: List[Dict], reviews_data: List[Dict]):
        """Save cleaned data without duplicates"""
        try:
            # Save movies
            movies_filepath = PROCESSED_DATA_DIR / 'movies_no_duplicates.json'
            with open(movies_filepath, 'w', encoding='utf-8') as f:
                json.dump(movies_data, f, indent=2, ensure_ascii=False)
            
            # Save reviews
            reviews_filepath = PROCESSED_DATA_DIR / 'reviews_no_duplicates.json'
            with open(reviews_filepath, 'w', encoding='utf-8') as f:
                json.dump(reviews_data, f, indent=2, ensure_ascii=False)
            
            # Save as CSV
            pd.DataFrame(movies_data).to_csv(
                PROCESSED_DATA_DIR / 'movies_no_duplicates.csv', 
                index=False, encoding='utf-8'
            )
            pd.DataFrame(reviews_data).to_csv(
                PROCESSED_DATA_DIR / 'reviews_no_duplicates.csv', 
                index=False, encoding='utf-8'
            )
            
            logger.info("Cleaned data saved successfully")
            
        except Exception as e:
            logger.error(f"Error saving cleaned data: {str(e)}")

if __name__ == "__main__":
    # Configure logging for standalone execution
    logging.basicConfig(level=logging.INFO)
    
    remover = DuplicateRemover()
    
    # Example usage with dummy data
    dummy_movies = [
        {'title': 'Test Movie', 'year': 2025, 'imdb_id': 'tt1234567'},
        {'title': 'Test Movie', 'year': 2025, 'imdb_id': 'tt1234567'},  # Exact duplicate
        {'title': 'Test Film', 'year': 2025, 'imdb_id': 'tt7654321'}   # Similar title
    ]
    
    dummy_reviews = [
        {'text': 'This is a great movie!', 'imdb_id': 'tt1234567'},
        {'text': 'This is a great movie!', 'imdb_id': 'tt1234567'},  # Exact duplicate
        {'text': 'This is a really great movie!', 'imdb_id': 'tt1234567'}  # Similar content
    ]
    
    unique_movies, unique_reviews, report = remover.remove_all_duplicates(dummy_movies, dummy_reviews)
    print(f"Removed duplicates: {report['total_duplicates_removed']}")
