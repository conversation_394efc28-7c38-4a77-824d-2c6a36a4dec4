{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# IMDB Aspect-Based Sentiment Analysis - Data Exploration\n", "\n", "This notebook provides initial data exploration for the IMDB movie reviews dataset."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import json\n", "from pathlib import Path\n", "import sys\n", "\n", "# Add project root to path\n", "project_root = Path.cwd().parent\n", "sys.path.append(str(project_root))\n", "\n", "from config import RAW_DATA_DIR, PROCESSED_DATA_DIR\n", "\n", "# Set plotting style\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Libraries imported successfully!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load <PERSON> Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load movies data\n", "try:\n", "    # Try TMDB data first\n", "    with open(RAW_DATA_DIR / 'tmdb_movies_2025.json', 'r', encoding='utf-8') as f:\n", "        movies_data = json.load(f)\n", "    print(f\"Loaded {len(movies_data)} movies from TMDB\")\nexcept FileNotFoundError:\n", "    try:\n", "        # Fallback to IMDB data\n", "        with open(RAW_DATA_DIR / 'imdb_movies_2025.json', 'r', encoding='utf-8') as f:\n", "            movies_data = json.load(f)\n", "        print(f\"Loaded {len(movies_data)} movies from IMDB\")\n", "    except FileNotFoundError:\n", "        print(\"No movie data found. Please run data collection first.\")\n", "        movies_data = []"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load reviews data\n", "try:\n", "    with open(RAW_DATA_DIR / 'movie_reviews_2025.json', 'r', encoding='utf-8') as f:\n", "        reviews_data = json.load(f)\n", "    print(f\"Loaded {len(reviews_data)} reviews\")\nexcept FileNotFoundError:\n", "    print(\"No review data found. Please run data collection first.\")\n", "    reviews_data = []"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Basic Data Overview"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Movies overview\n", "if movies_data:\n", "    movies_df = pd.DataFrame(movies_data)\n", "    print(\"Movies Dataset Overview:\")\n", "    print(f\"Shape: {movies_df.shape}\")\n", "    print(f\"Columns: {list(movies_df.columns)}\")\n", "    print(\"\\nFirst few rows:\")\n", "    display(movies_df.head())\n", "    \n", "    print(\"\\nData types:\")\n", "    print(movies_df.dtypes)\n", "    \n", "    print(\"\\nMissing values:\")\n", "    print(movies_df.isnull().sum())\nelse:\n", "    print(\"No movies data to display\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Reviews overview\n", "if reviews_data:\n", "    reviews_df = pd.DataFrame(reviews_data)\n", "    print(\"Reviews Dataset Overview:\")\n", "    print(f\"Shape: {reviews_df.shape}\")\n", "    print(f\"Columns: {list(reviews_df.columns)}\")\n", "    print(\"\\nFirst few rows:\")\n", "    display(reviews_df.head())\n", "    \n", "    print(\"\\nData types:\")\n", "    print(reviews_df.dtypes)\n", "    \n", "    print(\"\\nMissing values:\")\n", "    print(reviews_df.isnull().sum())\nelse:\n", "    print(\"No reviews data to display\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Data Quality Assessment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Movies data quality\n", "if movies_data:\n", "    print(\"Movies Data Quality Assessment:\")\n", "    \n", "    # Check for duplicates\n", "    if 'title' in movies_df.columns:\n", "        duplicates = movies_df.duplicated(subset=['title']).sum()\n", "        print(f\"Duplicate titles: {duplicates}\")\n", "    \n", "    # Rating distribution\n", "    rating_cols = ['rating', 'vote_average', 'imdb_rating']\n", "    for col in rating_cols:\n", "        if col in movies_df.columns:\n", "            valid_ratings = movies_df[col].dropna()\n", "            print(f\"\\n{col} statistics:\")\n", "            print(f\"Count: {len(valid_ratings)}\")\n", "            print(f\"Range: {valid_ratings.min():.2f} - {valid_ratings.max():.2f}\")\n", "            print(f\"Mean: {valid_ratings.mean():.2f}\")\n", "            break\n", "    \n", "    # Genre analysis\n", "    if 'genres' in movies_df.columns:\n", "        genre_counts = {}\n", "        for genres in movies_df['genres'].dropna():\n", "            if isinstance(genres, list):\n", "                for genre in genres:\n", "                    genre_counts[genre] = genre_counts.get(genre, 0) + 1\n", "        \n", "        print(f\"\\nTop 10 genres:\")\n", "        sorted_genres = sorted(genre_counts.items(), key=lambda x: x[1], reverse=True)[:10]\n", "        for genre, count in sorted_genres:\n", "            print(f\"{genre}: {count}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Reviews data quality\n", "if reviews_data:\n", "    print(\"Reviews Data Quality Assessment:\")\n", "    \n", "    # Text length analysis\n", "    if 'text' in reviews_df.columns:\n", "        text_lengths = reviews_df['text'].str.len()\n", "        print(f\"Text length statistics:\")\n", "        print(f\"Mean: {text_lengths.mean():.0f} characters\")\n", "        print(f\"Median: {text_lengths.median():.0f} characters\")\n", "        print(f\"Min: {text_lengths.min()} characters\")\n", "        print(f\"Max: {text_lengths.max()} characters\")\n", "        \n", "        # Short reviews\n", "        short_reviews = (text_lengths < 50).sum()\n", "        print(f\"Reviews shorter than 50 characters: {short_reviews} ({short_reviews/len(reviews_df)*100:.1f}%)\")\n", "    \n", "    # Rating distribution\n", "    if 'rating' in reviews_df.columns:\n", "        valid_ratings = reviews_df['rating'].dropna()\n", "        print(f\"\\nReview ratings:\")\n", "        print(f\"Count: {len(valid_ratings)}\")\n", "        print(f\"Range: {valid_ratings.min()} - {valid_ratings.max()}\")\n", "        print(f\"Mean: {valid_ratings.mean():.2f}\")\n", "    \n", "    # Source distribution\n", "    if 'source' in reviews_df.columns:\n", "        source_counts = reviews_df['source'].value_counts()\n", "        print(f\"\\nReviews by source:\")\n", "        for source, count in source_counts.items():\n", "            print(f\"{source}: {count}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Visualizations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Movie rating distribution\n", "if movies_data:\n", "    fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # Rating histogram\n", "    rating_cols = ['rating', 'vote_average', 'imdb_rating']\n", "    for col in rating_cols:\n", "        if col in movies_df.columns:\n", "            valid_ratings = movies_df[col].dropna()\n", "            axes[0].hist(valid_ratings, bins=20, alpha=0.7, edgecolor='black')\n", "            axes[0].set_title(f'Movie {col.title()} Distribution')\n", "            axes[0].set_xlabel('Rating')\n", "            axes[0].set_ylabel('Frequency')\n", "            break\n", "    \n", "    # Genre distribution\n", "    if 'genres' in movies_df.columns:\n", "        genre_counts = {}\n", "        for genres in movies_df['genres'].dropna():\n", "            if isinstance(genres, list):\n", "                for genre in genres:\n", "                    genre_counts[genre] = genre_counts.get(genre, 0) + 1\n", "        \n", "        top_genres = sorted(genre_counts.items(), key=lambda x: x[1], reverse=True)[:10]\n", "        genres, counts = zip(*top_genres)\n", "        \n", "        axes[1].bar(genres, counts)\n", "        axes[1].set_title('Top 10 Movie Genres')\n", "        axes[1].set_xlabel('Genre')\n", "        axes[1].set_ylabel('Count')\n", "        axes[1].tick_params(axis='x', rotation=45)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Review analysis visualizations\n", "if reviews_data:\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "    \n", "    # Text length distribution\n", "    if 'text' in reviews_df.columns:\n", "        text_lengths = reviews_df['text'].str.len()\n", "        axes[0, 0].hist(text_lengths, bins=30, alpha=0.7, edgecolor='black')\n", "        axes[0, 0].set_title('Review Text Length Distribution')\n", "        axes[0, 0].set_xlabel('Text Length (characters)')\n", "        axes[0, 0].set_ylabel('Frequency')\n", "    \n", "    # Rating distribution\n", "    if 'rating' in reviews_df.columns:\n", "        valid_ratings = reviews_df['rating'].dropna()\n", "        rating_counts = valid_ratings.value_counts().sort_index()\n", "        axes[0, 1].bar(rating_counts.index, rating_counts.values)\n", "        axes[0, 1].set_title('Review Rating Distribution')\n", "        axes[0, 1].set_xlabel('Rating')\n", "        axes[0, 1].set_ylabel('Count')\n", "    \n", "    # Source distribution\n", "    if 'source' in reviews_df.columns:\n", "        source_counts = reviews_df['source'].value_counts()\n", "        axes[1, 0].pie(source_counts.values, labels=source_counts.index, autopct='%1.1f%%')\n", "        axes[1, 0].set_title('Reviews by Source')\n", "    \n", "    # Word count distribution\n", "    if 'text' in reviews_df.columns:\n", "        word_counts = reviews_df['text'].str.split().str.len()\n", "        axes[1, 1].hist(word_counts, bins=30, alpha=0.7, edgecolor='black')\n", "        axes[1, 1].set_title('Review Word Count Distribution')\n", "        axes[1, 1].set_xlabel('Word Count')\n", "        axes[1, 1].set_ylabel('Frequency')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Sample Data Inspection"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sample movie data\n", "if movies_data:\n", "    print(\"Sample Movie Data:\")\n", "    print(\"=\" * 50)\n", "    \n", "    sample_movie = movies_data[0] if movies_data else {}\n", "    for key, value in sample_movie.items():\n", "        if isinstance(value, str) and len(value) > 100:\n", "            print(f\"{key}: {value[:100]}...\")\n", "        else:\n", "            print(f\"{key}: {value}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Sample review data\n", "if reviews_data:\n", "    print(\"Sample Review Data:\")\n", "    print(\"=\" * 50)\n", "    \n", "    sample_review = reviews_data[0] if reviews_data else {}\n", "    for key, value in sample_review.items():\n", "        if key == 'text' and isinstance(value, str) and len(value) > 200:\n", "            print(f\"{key}: {value[:200]}...\")\n", "        else:\n", "            print(f\"{key}: {value}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Data Relationships"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze relationship between movies and reviews\n", "if movies_data and reviews_data:\n", "    print(\"Movie-Review Relationships:\")\n", "    print(\"=\" * 30)\n", "    \n", "    # Count reviews per movie\n", "    movie_review_counts = {}\n", "    \n", "    for review in reviews_data:\n", "        movie_id = review.get('imdb_id') or review.get('tmdb_id') or review.get('movie_id')\n", "        if movie_id:\n", "            movie_review_counts[movie_id] = movie_review_counts.get(movie_id, 0) + 1\n", "    \n", "    if movie_review_counts:\n", "        review_counts = list(movie_review_counts.values())\n", "        print(f\"Movies with reviews: {len(movie_review_counts)}\")\n", "        print(f\"Average reviews per movie: {np.mean(review_counts):.1f}\")\n", "        print(f\"Median reviews per movie: {np.median(review_counts):.1f}\")\n", "        print(f\"Max reviews for a movie: {max(review_counts)}\")\n", "        print(f\"Min reviews for a movie: {min(review_counts)}\")\n", "        \n", "        # Plot distribution\n", "        plt.figure(figsize=(10, 6))\n", "        plt.hist(review_counts, bins=20, alpha=0.7, edgecolor='black')\n", "        plt.title('Distribution of Reviews per Movie')\n", "        plt.xlabel('Number of Reviews')\n", "        plt.ylabel('Number of Movies')\n", "        plt.show()\n", "    else:\n", "        print(\"No movie-review relationships found\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. <PERSON><PERSON><PERSON> and Next Steps"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Data Exploration Summary:\")\nprint(\"=\" * 30)\nprint(f\"Total movies: {len(movies_data)}\")\nprint(f\"Total reviews: {len(reviews_data)}\")\n\nif movies_data and reviews_data:\n    print(\"\\nData Quality Issues to Address:\")\n    print(\"- Text preprocessing needed for reviews\")\n    print(\"- Duplicate detection and removal\")\n    print(\"- Missing value handling\")\n    print(\"- Data validation and cleaning\")\n    \n    print(\"\\nNext Steps:\")\n    print(\"1. Run data cleaning pipeline\")\n    print(\"2. Perform aspect extraction\")\n    print(\"3. Conduct sentiment analysis\")\n    print(\"4. Generate visualizations and reports\")\nelse:\n    print(\"\\nPlease run data collection first to gather movie and review data.\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}