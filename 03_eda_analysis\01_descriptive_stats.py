"""
Descriptive Statistics Module
Generates comprehensive statistical analysis of movie and review data
"""

import pandas as pd
import numpy as np
import json
import logging
from typing import List, Dict, Optional, Tuple
from pathlib import Path
import sys
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import scipy.stats as stats

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from config import PROCESSED_DATA_DIR, RESULTS_DIR, PLOT_STYLE, FIGURE_SIZE

logger = logging.getLogger(__name__)

class DescriptiveAnalysis:
    """Comprehensive descriptive statistical analysis"""
    
    def __init__(self):
        self.stats_report = {
            'movies': {},
            'reviews': {},
            'correlations': {},
            'distributions': {},
            'summary': {}
        }
        
        # Set plotting style
        plt.style.use(PLOT_STYLE)
        sns.set_palette("husl")
    
    def analyze_movies_statistics(self, movies_data: List[Dict]) -> Dict:
        """Generate comprehensive movie statistics"""
        logger.info(f"Analyzing statistics for {len(movies_data)} movies")
        
        # Convert to DataFrame for easier analysis
        df = pd.DataFrame(movies_data)
        
        stats = {
            'basic_stats': self._calculate_basic_movie_stats(df),
            'rating_analysis': self._analyze_movie_ratings(df),
            'genre_analysis': self._analyze_movie_genres(df),
            'year_analysis': self._analyze_movie_years(df),
            'budget_revenue_analysis': self._analyze_budget_revenue(df),
            'runtime_analysis': self._analyze_runtime(df)
        }
        
        self.stats_report['movies'] = stats
        return stats
    
    def _calculate_basic_movie_stats(self, df: pd.DataFrame) -> Dict:
        """Calculate basic movie statistics"""
        stats = {
            'total_movies': len(df),
            'unique_titles': df['title'].nunique() if 'title' in df.columns else 0,
            'movies_with_ratings': df['rating'].notna().sum() if 'rating' in df.columns else 0,
            'movies_with_genres': df['genres'].notna().sum() if 'genres' in df.columns else 0,
            'movies_with_plot': 0
        }
        
        # Count movies with plot/overview
        plot_fields = ['plot', 'overview']
        for field in plot_fields:
            if field in df.columns:
                stats['movies_with_plot'] += df[field].notna().sum()
                break
        
        # Data completeness
        stats['data_completeness'] = {
            'rating_completeness': stats['movies_with_ratings'] / stats['total_movies'] if stats['total_movies'] > 0 else 0,
            'genre_completeness': stats['movies_with_genres'] / stats['total_movies'] if stats['total_movies'] > 0 else 0,
            'plot_completeness': stats['movies_with_plot'] / stats['total_movies'] if stats['total_movies'] > 0 else 0
        }
        
        return stats
    
    def _analyze_movie_ratings(self, df: pd.DataFrame) -> Dict:
        """Analyze movie ratings distribution"""
        rating_cols = ['rating', 'vote_average', 'imdb_rating']
        rating_col = None
        
        for col in rating_cols:
            if col in df.columns and df[col].notna().sum() > 0:
                rating_col = col
                break
        
        if not rating_col:
            return {'error': 'No rating data available'}
        
        ratings = df[rating_col].dropna()
        
        analysis = {
            'count': len(ratings),
            'mean': float(ratings.mean()),
            'median': float(ratings.median()),
            'std': float(ratings.std()),
            'min': float(ratings.min()),
            'max': float(ratings.max()),
            'quartiles': {
                'q1': float(ratings.quantile(0.25)),
                'q2': float(ratings.quantile(0.5)),
                'q3': float(ratings.quantile(0.75))
            },
            'distribution': {
                'skewness': float(stats.skew(ratings)),
                'kurtosis': float(stats.kurtosis(ratings))
            }
        }
        
        # Rating categories
        analysis['rating_categories'] = {
            'excellent': (ratings >= 8.0).sum(),
            'good': ((ratings >= 6.0) & (ratings < 8.0)).sum(),
            'average': ((ratings >= 4.0) & (ratings < 6.0)).sum(),
            'poor': (ratings < 4.0).sum()
        }
        
        return analysis
    
    def _analyze_movie_genres(self, df: pd.DataFrame) -> Dict:
        """Analyze movie genres distribution"""
        if 'genres' not in df.columns:
            return {'error': 'No genre data available'}
        
        # Extract all genres
        all_genres = []
        for genres in df['genres'].dropna():
            if isinstance(genres, list):
                all_genres.extend(genres)
            elif isinstance(genres, str):
                all_genres.extend([g.strip() for g in genres.split(',')])
        
        genre_counts = Counter(all_genres)
        
        analysis = {
            'total_genre_instances': len(all_genres),
            'unique_genres': len(genre_counts),
            'top_genres': dict(genre_counts.most_common(10)),
            'genre_distribution': dict(genre_counts),
            'avg_genres_per_movie': len(all_genres) / len(df) if len(df) > 0 else 0
        }
        
        return analysis
    
    def _analyze_movie_years(self, df: pd.DataFrame) -> Dict:
        """Analyze movie release years"""
        if 'year' not in df.columns:
            return {'error': 'No year data available'}
        
        years = df['year'].dropna()
        
        if len(years) == 0:
            return {'error': 'No valid year data'}
        
        analysis = {
            'year_range': {
                'min': int(years.min()),
                'max': int(years.max())
            },
            'year_distribution': dict(years.value_counts().sort_index()),
            'movies_per_year': {
                'mean': float(years.value_counts().mean()),
                'median': float(years.value_counts().median()),
                'std': float(years.value_counts().std())
            }
        }
        
        return analysis
    
    def _analyze_budget_revenue(self, df: pd.DataFrame) -> Dict:
        """Analyze budget and revenue data"""
        analysis = {}
        
        # Budget analysis
        if 'budget' in df.columns:
            budget = df['budget'].dropna()
            budget = budget[budget > 0]  # Remove zero budgets
            
            if len(budget) > 0:
                analysis['budget'] = {
                    'count': len(budget),
                    'mean': float(budget.mean()),
                    'median': float(budget.median()),
                    'std': float(budget.std()),
                    'min': float(budget.min()),
                    'max': float(budget.max())
                }
        
        # Revenue analysis
        if 'revenue' in df.columns:
            revenue = df['revenue'].dropna()
            revenue = revenue[revenue > 0]  # Remove zero revenues
            
            if len(revenue) > 0:
                analysis['revenue'] = {
                    'count': len(revenue),
                    'mean': float(revenue.mean()),
                    'median': float(revenue.median()),
                    'std': float(revenue.std()),
                    'min': float(revenue.min()),
                    'max': float(revenue.max())
                }
        
        # ROI analysis
        if 'budget' in df.columns and 'revenue' in df.columns:
            valid_data = df[(df['budget'] > 0) & (df['revenue'] > 0)]
            if len(valid_data) > 0:
                roi = (valid_data['revenue'] - valid_data['budget']) / valid_data['budget'] * 100
                analysis['roi'] = {
                    'count': len(roi),
                    'mean': float(roi.mean()),
                    'median': float(roi.median()),
                    'std': float(roi.std()),
                    'min': float(roi.min()),
                    'max': float(roi.max())
                }
        
        return analysis
    
    def _analyze_runtime(self, df: pd.DataFrame) -> Dict:
        """Analyze movie runtime"""
        if 'runtime' not in df.columns:
            return {'error': 'No runtime data available'}
        
        runtime = df['runtime'].dropna()
        runtime = runtime[runtime > 0]  # Remove invalid runtimes
        
        if len(runtime) == 0:
            return {'error': 'No valid runtime data'}
        
        analysis = {
            'count': len(runtime),
            'mean': float(runtime.mean()),
            'median': float(runtime.median()),
            'std': float(runtime.std()),
            'min': float(runtime.min()),
            'max': float(runtime.max()),
            'runtime_categories': {
                'short': (runtime < 90).sum(),
                'standard': ((runtime >= 90) & (runtime < 120)).sum(),
                'long': ((runtime >= 120) & (runtime < 180)).sum(),
                'very_long': (runtime >= 180).sum()
            }
        }
        
        return analysis
    
    def analyze_reviews_statistics(self, reviews_data: List[Dict]) -> Dict:
        """Generate comprehensive review statistics"""
        logger.info(f"Analyzing statistics for {len(reviews_data)} reviews")
        
        # Convert to DataFrame
        df = pd.DataFrame(reviews_data)
        
        stats = {
            'basic_stats': self._calculate_basic_review_stats(df),
            'text_analysis': self._analyze_review_text(df),
            'rating_analysis': self._analyze_review_ratings(df),
            'source_analysis': self._analyze_review_sources(df),
            'temporal_analysis': self._analyze_review_temporal(df)
        }
        
        self.stats_report['reviews'] = stats
        return stats
    
    def _calculate_basic_review_stats(self, df: pd.DataFrame) -> Dict:
        """Calculate basic review statistics"""
        stats = {
            'total_reviews': len(df),
            'reviews_with_ratings': df['rating'].notna().sum() if 'rating' in df.columns else 0,
            'reviews_with_text': df['text'].notna().sum() if 'text' in df.columns else 0,
            'unique_movies': 0,
            'unique_authors': df['author'].nunique() if 'author' in df.columns else 0
        }
        
        # Count unique movies
        movie_id_cols = ['imdb_id', 'tmdb_id', 'movie_id']
        for col in movie_id_cols:
            if col in df.columns:
                stats['unique_movies'] = df[col].nunique()
                break
        
        return stats
    
    def _analyze_review_text(self, df: pd.DataFrame) -> Dict:
        """Analyze review text characteristics"""
        if 'text' not in df.columns:
            return {'error': 'No text data available'}
        
        texts = df['text'].dropna()
        
        # Calculate text lengths
        text_lengths = texts.str.len()
        word_counts = texts.str.split().str.len()
        
        analysis = {
            'text_length': {
                'mean': float(text_lengths.mean()),
                'median': float(text_lengths.median()),
                'std': float(text_lengths.std()),
                'min': int(text_lengths.min()),
                'max': int(text_lengths.max())
            },
            'word_count': {
                'mean': float(word_counts.mean()),
                'median': float(word_counts.median()),
                'std': float(word_counts.std()),
                'min': int(word_counts.min()),
                'max': int(word_counts.max())
            },
            'length_categories': {
                'short': (text_lengths < 100).sum(),
                'medium': ((text_lengths >= 100) & (text_lengths < 500)).sum(),
                'long': ((text_lengths >= 500) & (text_lengths < 1000)).sum(),
                'very_long': (text_lengths >= 1000).sum()
            }
        }
        
        return analysis
    
    def _analyze_review_ratings(self, df: pd.DataFrame) -> Dict:
        """Analyze review ratings"""
        if 'rating' not in df.columns:
            return {'error': 'No rating data available'}
        
        ratings = df['rating'].dropna()
        
        if len(ratings) == 0:
            return {'error': 'No valid rating data'}
        
        analysis = {
            'count': len(ratings),
            'mean': float(ratings.mean()),
            'median': float(ratings.median()),
            'std': float(ratings.std()),
            'min': float(ratings.min()),
            'max': float(ratings.max()),
            'rating_distribution': dict(ratings.value_counts().sort_index())
        }
        
        return analysis
    
    def _analyze_review_sources(self, df: pd.DataFrame) -> Dict:
        """Analyze review sources"""
        if 'source' not in df.columns:
            return {'error': 'No source data available'}
        
        sources = df['source'].value_counts()
        
        analysis = {
            'source_distribution': dict(sources),
            'total_sources': len(sources),
            'most_common_source': sources.index[0] if len(sources) > 0 else None
        }
        
        return analysis
    
    def _analyze_review_temporal(self, df: pd.DataFrame) -> Dict:
        """Analyze temporal patterns in reviews"""
        date_cols = ['date', 'created_at', 'updated_at']
        date_col = None
        
        for col in date_cols:
            if col in df.columns and df[col].notna().sum() > 0:
                date_col = col
                break
        
        if not date_col:
            return {'error': 'No date data available'}
        
        # Convert to datetime
        try:
            dates = pd.to_datetime(df[date_col], errors='coerce').dropna()
            
            if len(dates) == 0:
                return {'error': 'No valid date data'}
            
            analysis = {
                'date_range': {
                    'earliest': dates.min().isoformat(),
                    'latest': dates.max().isoformat()
                },
                'reviews_by_year': dict(dates.dt.year.value_counts().sort_index()),
                'reviews_by_month': dict(dates.dt.month.value_counts().sort_index())
            }
            
            return analysis
            
        except Exception as e:
            logger.warning(f"Error analyzing temporal data: {str(e)}")
            return {'error': f'Error processing dates: {str(e)}'}
    
    def generate_statistics(self, movies_data: List[Dict], reviews_data: List[Dict]) -> Dict:
        """Generate comprehensive statistics report"""
        logger.info("Generating comprehensive statistics report")
        
        # Analyze movies
        movie_stats = self.analyze_movies_statistics(movies_data)
        
        # Analyze reviews
        review_stats = self.analyze_reviews_statistics(reviews_data)
        
        # Generate summary
        summary = {
            'total_movies': len(movies_data),
            'total_reviews': len(reviews_data),
            'avg_reviews_per_movie': len(reviews_data) / len(movies_data) if movies_data else 0,
            'analysis_timestamp': datetime.now().isoformat()
        }
        
        self.stats_report['summary'] = summary
        
        # Save report
        self.save_statistics_report()
        
        return self.stats_report
    
    def save_statistics_report(self, filename: str = 'descriptive_statistics.json'):
        """Save statistics report to file"""
        filepath = RESULTS_DIR / filename
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.stats_report, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Statistics report saved to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving statistics report: {str(e)}")

if __name__ == "__main__":
    # Configure logging for standalone execution
    logging.basicConfig(level=logging.INFO)
    
    analyzer = DescriptiveAnalysis()
    
    # Example usage with dummy data
    dummy_movies = [
        {'title': 'Movie 1', 'year': 2025, 'rating': 8.5, 'genres': ['Action', 'Drama']},
        {'title': 'Movie 2', 'year': 2025, 'rating': 7.2, 'genres': ['Comedy']}
    ]
    
    dummy_reviews = [
        {'text': 'Great movie with excellent acting!', 'rating': 9, 'source': 'imdb'},
        {'text': 'Not bad, but could be better.', 'rating': 6, 'source': 'tmdb'}
    ]
    
    report = analyzer.generate_statistics(dummy_movies, dummy_reviews)
    print("Statistics report generated successfully")
