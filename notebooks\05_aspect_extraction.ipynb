{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Aspect Extraction from Movie Reviews\n", "\n", "This notebook demonstrates the extraction of movie aspects from review text for aspect-based sentiment analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import json\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import sys\n", "from collections import Counter, defaultdict\n", "import networkx as nx\n", "from wordcloud import WordCloud\n", "\n", "# Add project root to path\n", "project_root = Path.cwd().parent\n", "sys.path.append(str(project_root))\n", "\n", "# Import project modules\n", "from config import PROCESSED_DATA_DIR, MOVIE_ASPECTS, ASPECT_KEYWORDS\n", "from 04_sentiment_analysis.01_aspect_extraction import AspectExtractor\n", "\n", "print(\"Libraries and modules imported successfully!\")\n", "print(f\"Target aspects: {MOVIE_ASPECTS}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Load Processed Review Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load processed review data\n", "try:\n", "    with open(PROCESSED_DATA_DIR / 'processed_reviews.json', 'r', encoding='utf-8') as f:\n", "        reviews_data = json.load(f)\n", "    print(f\"✅ Loaded {len(reviews_data)} processed reviews\")\nexcept FileNotFoundError:\n", "    print(\"⚠️ No processed review data found. Creating sample data for demonstration.\")\n", "    reviews_data = [\n", "        {\n", "            'text': 'The acting was excellent and the plot was engaging. The cinematography was beautiful.',\n", "            'processed_text': 'acting excellent plot engaging cinematography beautiful',\n", "            'rating': 9,\n", "            'source': 'imdb',\n", "            'imdb_id': 'tt1234567'\n", "        },\n", "        {\n", "            'text': 'Poor direction and terrible dialogue. The music was the only good thing.',\n", "            'processed_text': 'poor direction terrible dialogue music good thing',\n", "            'rating': 3,\n", "            'source': 'imdb',\n", "            'imdb_id': 'tt7654321'\n", "        },\n", "        {\n", "            'text': 'Great characters and amazing effects. The pacing was perfect.',\n", "            'processed_text': 'great characters amazing effects pacing perfect',\n", "            'rating': 8,\n", "            'source': 'tmdb',\n", "            'tmdb_id': 12345\n", "        }\n", "    ]\n", "\n", "print(f\"\\nTotal reviews for aspect extraction: {len(reviews_data)}\")\n", "\n", "# Show sample review\n", "if reviews_data:\n", "    print(\"\\nSample review:\")\n", "    sample = reviews_data[0]\n", "    print(f\"Text: {sample['text']}\")\n", "    print(f\"Rating: {sample.get('rating', 'N/A')}\")\n", "    print(f\"Source: {sample.get('source', 'N/A')}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Aspect Extraction Configuration"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display aspect configuration\n", "print(\"🎬 Movie Aspects Configuration\")\n", "print(\"=\" * 35)\n", "\n", "for aspect, keywords in ASPECT_KEYWORDS.items():\n", "    print(f\"\\n{aspect.upper()}:\")\n", "    print(f\"  Keywords: {', '.join(keywords)}\")\n", "\n", "print(f\"\\nTotal aspects to extract: {len(MOVIE_ASPECTS)}\")\n", "print(f\"Total keywords: {sum(len(keywords) for keywords in ASPECT_KEYWORDS.values())}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Single Text Aspect Extraction Demo"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize aspect extractor\n", "extractor = AspectExtractor()\n", "\n", "# Demonstrate aspect extraction on sample texts\n", "sample_texts = [\n", "    \"The acting was excellent and the plot was engaging. The cinematography was beautiful but the dialogue felt forced.\",\n", "    \"Poor direction ruined what could have been a great movie. The music was amazing though.\",\n", "    \"Great characters and stunning visual effects. The pacing was perfect throughout the film.\"\n", "]\n", "\n", "print(\"🔍 Aspect Extraction Demonstration\")\n", "print(\"=\" * 40)\n", "\n", "for i, text in enumerate(sample_texts, 1):\n", "    print(f\"\\nExample {i}:\")\n", "    print(f\"Text: {text}\")\n", "    \n", "    # Extract aspects\n", "    aspects = extractor.extract_aspects_from_text(text)\n", "    \n", "    print(f\"Extracted aspects: {list(aspects.keys())}\")\n", "    \n", "    # Show details for each aspect\n", "    for aspect, sentences in aspects.items():\n", "        print(f\"  {aspect}: {sentences}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Bulk Aspect Extraction"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Extract aspects from all reviews\n", "print(\"🚀 Starting bulk aspect extraction...\")\n", "\n", "# Perform aspect extraction\n", "enriched_reviews = extractor.extract_aspects_from_reviews(reviews_data)\n", "\n", "print(f\"✅ Aspect extraction completed!\")\n", "print(f\"Processed {len(enriched_reviews)} reviews\")\n", "\n", "# Show sample enriched review\n", "if enriched_reviews:\n", "    print(\"\\nSample enriched review:\")\n", "    sample = enriched_reviews[0]\n", "    print(f\"Original text: {sample['text']}\")\n", "    print(f\"Extracted aspects: {sample.get('mentioned_aspects', [])}\")\n", "    print(f\"Aspect count: {sample.get('aspect_count', 0)}\")\n", "    \n", "    if sample.get('extracted_aspects'):\n", "        print(\"\\nDetailed aspects:\")\n", "        for aspect, sentences in sample['extracted_aspects'].items():\n", "            print(f\"  {aspect}: {sentences}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Aspect Extraction Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Generate aspect extraction summary\n", "summary = extractor.get_aspect_summary(enriched_reviews)\n", "\n", "print(\"📊 Aspect Extraction Summary\")\n", "print(\"=\" * 30)\n", "print(f\"Total reviews: {summary['total_reviews']}\")\n", "print(f\"Reviews with aspects: {summary['reviews_with_aspects']}\")\n", "print(f\"Aspect coverage: {summary['aspect_coverage']:.2%}\")\n", "print(f\"Total aspect mentions: {summary['total_aspect_mentions']}\")\n", "print(f\"Unique aspects found: {summary['unique_aspects_found']}\")\n", "print(f\"Avg aspects per review: {summary['avg_aspects_per_review']:.2f}\")\n", "\n", "print(\"\\nMost common aspects:\")\n", "for aspect, count in summary['most_common_aspects'].items():\n", "    print(f\"  {aspect}: {count}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Aspect Co-occurrence Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze aspect co-occurrence\n", "print(\"🔗 Analyzing aspect co-occurrence...\")\n", "\n", "cooccurrence_analysis = extractor.analyze_aspect_cooccurrence(enriched_reviews)\n", "\n", "if 'top_cooccurring_pairs' in cooccurrence_analysis:\n", "    print(\"\\nTop co-occurring aspect pairs:\")\n", "    for pair_info in cooccurrence_analysis['top_cooccurring_pairs'][:10]:\n", "        aspects = pair_info['aspects']\n", "        count = pair_info['count']\n", "        print(f\"  {aspects[0]} + {aspects[1]}: {count} times\")\n", "\n", "# Create co-occurrence matrix visualization\n", "if enriched_reviews and len(enriched_reviews) > 1:\n", "    # Collect aspect co-occurrence data\n", "    aspect_pairs = defaultdict(int)\n", "    \n", "    for review in enriched_reviews:\n", "        aspects = review.get('mentioned_aspects', [])\n", "        for i, aspect1 in enumerate(aspects):\n", "            for j, aspect2 in enumerate(aspects):\n", "                if i < j:  # Avoid duplicates\n", "                    pair = tuple(sorted([aspect1, aspect2]))\n", "                    aspect_pairs[pair] += 1\n", "    \n", "    if aspect_pairs:\n", "        # Create network graph\n", "        plt.figure(figsize=(12, 8))\n", "        \n", "        G = nx.Graph()\n", "        \n", "        # Add edges with weights\n", "        for (aspect1, aspect2), weight in aspect_pairs.items():\n", "            if weight > 0:  # Only add if there's co-occurrence\n", "                G.add_edge(aspect1, aspect2, weight=weight)\n", "        \n", "        if G.nodes():\n", "            # Draw network\n", "            pos = nx.spring_layout(G, k=2, iterations=50)\n", "            \n", "            # Draw nodes\n", "            nx.draw_networkx_nodes(G, pos, node_color='lightblue', \n", "                                 node_size=1000, alpha=0.7)\n", "            \n", "            # Draw edges with thickness based on weight\n", "            edges = G.edges()\n", "            weights = [G[u][v]['weight'] for u, v in edges]\n", "            nx.draw_networkx_edges(G, pos, width=[w*2 for w in weights], \n", "                                 alpha=0.6, edge_color='gray')\n", "            \n", "            # Draw labels\n", "            nx.draw_networkx_labels(G, pos, font_size=10, font_weight='bold')\n", "            \n", "            plt.title('Aspect Co-occurrence Network', fontsize=16, fontweight='bold')\n", "            plt.axis('off')\n", "            plt.tight_layout()\n", "            plt.show()\n", "        else:\n", "            print(\"No co-occurrences found for network visualization\")\n", "    else:\n", "        print(\"No aspect co-occurrences found\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Aspect Distribution Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize aspect distribution\n", "if enriched_reviews:\n", "    # Collect all aspects\n", "    all_aspects = []\n", "    for review in enriched_reviews:\n", "        all_aspects.extend(review.get('mentioned_aspects', []))\n", "    \n", "    if all_aspects:\n", "        aspect_counts = Counter(all_aspects)\n", "        \n", "        # Create visualizations\n", "        fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "        fig.suptitle('Aspect Extraction Analysis', fontsize=16, fontweight='bold')\n", "        \n", "        # 1. Aspect frequency bar chart\n", "        aspects = list(aspect_counts.keys())\n", "        counts = list(aspect_counts.values())\n", "        \n", "        axes[0, 0].bar(aspects, counts, color='skyblue', alpha=0.7)\n", "        axes[0, 0].set_title('Aspect Frequency')\n", "        axes[0, 0].set_xlabel('Aspect')\n", "        axes[0, 0].set_ylabel('Frequency')\n", "        axes[0, 0].tick_params(axis='x', rotation=45)\n", "        \n", "        # 2. Aspect distribution pie chart\n", "        axes[0, 1].pie(counts, labels=aspects, autopct='%1.1f%%', startangle=90)\n", "        axes[0, 1].set_title('Aspect Distribution')\n", "        \n", "        # 3. Reviews with aspects histogram\n", "        aspect_counts_per_review = [review.get('aspect_count', 0) for review in enriched_reviews]\n", "        axes[1, 0].hist(aspect_counts_per_review, bins=max(1, len(set(aspect_counts_per_review))), \n", "                       alpha=0.7, color='lightgreen', edgecolor='black')\n", "        axes[1, 0].set_title('Aspects per Review Distribution')\n", "        axes[1, 0].set_xlabel('Number of Aspects')\n", "        axes[1, 0].set_ylabel('Number of Reviews')\n", "        \n", "        # 4. Aspect coverage by review source\n", "        if 'source' in enriched_reviews[0]:\n", "            source_aspect_data = defaultdict(list)\n", "            for review in enriched_reviews:\n", "                source = review.get('source', 'unknown')\n", "                aspect_count = review.get('aspect_count', 0)\n", "                source_aspect_data[source].append(aspect_count)\n", "            \n", "            sources = list(source_aspect_data.keys())\n", "            avg_aspects = [np.mean(source_aspect_data[source]) for source in sources]\n", "            \n", "            axes[1, 1].bar(sources, avg_aspects, color='orange', alpha=0.7)\n", "            axes[1, 1].set_title('Average Aspects per Review by Source')\n", "            axes[1, 1].set_xlabel('Source')\n", "            axes[1, 1].set_ylabel('Average Aspects')\n", "        else:\n", "            axes[1, 1].text(0.5, 0.5, 'No source data available', \n", "                           ha='center', va='center', fontsize=12)\n", "            axes[1, 1].set_title('Source Analysis')\n", "        \n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "        # Create aspect word cloud\n", "        if len(all_aspects) > 1:\n", "            aspect_text = ' '.join(all_aspects)\n", "            wordcloud = WordCloud(width=800, height=400, \n", "                                background_color='white',\n", "                                colormap='viridis').generate(aspect_text)\n", "            \n", "            plt.figure(figsize=(12, 6))\n", "            plt.imshow(wordcloud, interpolation='bilinear')\n", "            plt.axis('off')\n", "            plt.title('Aspect Word Cloud', fontsize=16, fontweight='bold')\n", "            plt.tight_layout()\n", "            plt.show()\n", "    else:\n", "        print(\"No aspects found for visualization\")\nelse:\n", "    print(\"No enriched reviews available for visualization\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Save Extracted Aspects"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save enriched reviews with extracted aspects\n", "print(\"💾 Saving extracted aspects...\")\n", "\n", "# Save to processed data directory\n", "extractor.save_extracted_aspects(enriched_reviews, 'reviews_with_aspects.json')\n", "\n", "# Create summary report\n", "aspect_report = {\n", "    'extraction_summary': summary,\n", "    'cooccurrence_analysis': cooccurrence_analysis,\n", "    'aspect_configuration': {\n", "        'target_aspects': MOVIE_ASPECTS,\n", "        'aspect_keywords': ASPECT_KEYWORDS\n", "    },\n", "    'extraction_statistics': {\n", "        'total_reviews_processed': len(enriched_reviews),\n", "        'reviews_with_aspects': len([r for r in enriched_reviews if r.get('aspect_count', 0) > 0]),\n", "        'total_aspect_mentions': sum(r.get('aspect_count', 0) for r in enriched_reviews)\n", "    }\n", "}\n", "\n", "# Save aspect report\n", "report_file = PROCESSED_DATA_DIR / 'aspect_extraction_report.json'\n", "with open(report_file, 'w', encoding='utf-8') as f:\n", "    json.dump(aspect_report, f, indent=2, ensure_ascii=False)\n", "\n", "print(f\"✅ Aspect extraction report saved to {report_file}\")\n", "\n", "print(\"\\n🎉 Aspect extraction analysis complete!\")\n", "print(\"\\nNext steps:\")\n", "print(\"1. Run sentiment analysis on extracted aspects (06_sentiment_analysis.ipynb)\")\n", "print(\"2. Analyze aspect-based sentiment patterns\")\n", "print(\"3. Generate comprehensive reports\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "This notebook demonstrated:\n", "\n", "1. **Aspect Configuration**: Understanding the predefined movie aspects and keywords\n", "2. **Single Text Extraction**: Demonstrating aspect extraction on sample texts\n", "3. **Bulk Extraction**: Processing all reviews to extract aspects\n", "4. **Co-occurrence Analysis**: Understanding which aspects are mentioned together\n", "5. **Distribution Analysis**: Visualizing aspect frequency and patterns\n", "6. **Network Visualization**: Showing relationships between aspects\n", "7. **Data Storage**: Saving enriched reviews with extracted aspects\n", "\n", "The extracted aspects provide the foundation for aspect-based sentiment analysis, allowing us to understand sentiment towards specific movie components rather than just overall sentiment."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}