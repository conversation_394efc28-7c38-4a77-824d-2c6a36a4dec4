"""
Utility functions for data cleaning and preprocessing
"""

import re
import string
import unicodedata
from typing import List, Dict, Optional, Union
import pandas as pd
import numpy as np
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class TextCleaner:
    """Utility class for text cleaning operations"""
    
    def __init__(self):
        # Common contractions mapping
        self.contractions = {
            "ain't": "is not", "aren't": "are not", "can't": "cannot",
            "couldn't": "could not", "didn't": "did not", "doesn't": "does not",
            "don't": "do not", "hadn't": "had not", "hasn't": "has not",
            "haven't": "have not", "he'd": "he would", "he'll": "he will",
            "he's": "he is", "i'd": "i would", "i'll": "i will",
            "i'm": "i am", "i've": "i have", "isn't": "is not",
            "it'd": "it would", "it'll": "it will", "it's": "it is",
            "let's": "let us", "shouldn't": "should not", "that's": "that is",
            "there's": "there is", "they'd": "they would", "they'll": "they will",
            "they're": "they are", "they've": "they have", "we'd": "we would",
            "we're": "we are", "we've": "we have", "weren't": "were not",
            "what's": "what is", "where's": "where is", "who's": "who is",
            "won't": "will not", "wouldn't": "would not", "you'd": "you would",
            "you'll": "you will", "you're": "you are", "you've": "you have"
        }
        
        # HTML entities mapping
        self.html_entities = {
            '&amp;': '&', '&lt;': '<', '&gt;': '>', '&quot;': '"',
            '&#39;': "'", '&nbsp;': ' ', '&copy;': '©', '&reg;': '®'
        }
    
    def remove_html_tags(self, text: str) -> str:
        """Remove HTML tags from text"""
        if not text:
            return ""
        
        # Remove HTML tags
        clean_text = re.sub(r'<[^>]+>', '', text)
        
        # Decode HTML entities
        for entity, replacement in self.html_entities.items():
            clean_text = clean_text.replace(entity, replacement)
        
        return clean_text
    
    def normalize_unicode(self, text: str) -> str:
        """Normalize unicode characters"""
        if not text:
            return ""
        
        # Normalize unicode
        text = unicodedata.normalize('NFKD', text)
        
        # Remove non-ASCII characters (optional, can be commented out)
        # text = text.encode('ascii', 'ignore').decode('ascii')
        
        return text
    
    def expand_contractions(self, text: str) -> str:
        """Expand contractions in text"""
        if not text:
            return ""
        
        text_lower = text.lower()
        for contraction, expansion in self.contractions.items():
            text_lower = text_lower.replace(contraction, expansion)
        
        return text_lower
    
    def remove_special_characters(self, text: str, keep_punctuation: bool = True) -> str:
        """Remove special characters from text"""
        if not text:
            return ""
        
        if keep_punctuation:
            # Keep basic punctuation
            pattern = r'[^a-zA-Z0-9\s\.\,\!\?\;\:\-\'\"]'
        else:
            # Remove all special characters except spaces
            pattern = r'[^a-zA-Z0-9\s]'
        
        clean_text = re.sub(pattern, '', text)
        return clean_text
    
    def normalize_whitespace(self, text: str) -> str:
        """Normalize whitespace in text"""
        if not text:
            return ""
        
        # Replace multiple whitespace with single space
        text = re.sub(r'\s+', ' ', text)
        
        # Strip leading and trailing whitespace
        text = text.strip()
        
        return text
    
    def remove_urls(self, text: str) -> str:
        """Remove URLs from text"""
        if not text:
            return ""
        
        # Remove URLs
        url_pattern = r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        text = re.sub(url_pattern, '', text)
        
        # Remove www links
        www_pattern = r'www\.(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        text = re.sub(www_pattern, '', text)
        
        return text
    
    def remove_email_addresses(self, text: str) -> str:
        """Remove email addresses from text"""
        if not text:
            return ""
        
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        text = re.sub(email_pattern, '', text)
        
        return text
    
    def clean_text_comprehensive(self, text: str, 
                                expand_contractions: bool = True,
                                remove_html: bool = True,
                                remove_urls: bool = True,
                                remove_emails: bool = True,
                                normalize_unicode: bool = True,
                                remove_special_chars: bool = False,
                                keep_punctuation: bool = True) -> str:
        """Comprehensive text cleaning"""
        if not text:
            return ""
        
        # Remove HTML tags
        if remove_html:
            text = self.remove_html_tags(text)
        
        # Remove URLs
        if remove_urls:
            text = self.remove_urls(text)
        
        # Remove email addresses
        if remove_emails:
            text = self.remove_email_addresses(text)
        
        # Normalize unicode
        if normalize_unicode:
            text = self.normalize_unicode(text)
        
        # Expand contractions
        if expand_contractions:
            text = self.expand_contractions(text)
        
        # Remove special characters
        if remove_special_chars:
            text = self.remove_special_characters(text, keep_punctuation)
        
        # Normalize whitespace
        text = self.normalize_whitespace(text)
        
        return text

class DataValidator:
    """Utility class for data validation"""
    
    @staticmethod
    def validate_movie_data(movie: Dict) -> Dict:
        """Validate and clean movie data"""
        validated = {}
        
        # Required fields
        validated['title'] = str(movie.get('title', '')).strip()
        
        # Optional fields with validation
        year = movie.get('year')
        if year:
            try:
                validated['year'] = int(year)
                if validated['year'] < 1900 or validated['year'] > 2030:
                    validated['year'] = None
            except (ValueError, TypeError):
                validated['year'] = None
        
        # Rating validation
        rating = movie.get('rating') or movie.get('vote_average') or movie.get('imdb_rating')
        if rating:
            try:
                validated['rating'] = float(rating)
                if validated['rating'] < 0 or validated['rating'] > 10:
                    validated['rating'] = None
            except (ValueError, TypeError):
                validated['rating'] = None
        
        # Genres validation
        genres = movie.get('genres', [])
        if isinstance(genres, list):
            validated['genres'] = [str(g).strip() for g in genres if g]
        elif isinstance(genres, str):
            validated['genres'] = [g.strip() for g in genres.split(',') if g.strip()]
        else:
            validated['genres'] = []
        
        # Copy other fields
        for field in ['imdb_id', 'tmdb_id', 'overview', 'plot', 'runtime', 'budget', 'revenue']:
            if field in movie:
                validated[field] = movie[field]
        
        return validated
    
    @staticmethod
    def validate_review_data(review: Dict) -> Dict:
        """Validate and clean review data"""
        validated = {}
        
        # Required fields
        validated['text'] = str(review.get('text', '')).strip()
        validated['source'] = str(review.get('source', 'unknown')).strip()
        
        # Movie ID validation
        for id_field in ['imdb_id', 'tmdb_id', 'movie_id']:
            if id_field in review:
                validated[id_field] = review[id_field]
        
        # Rating validation
        rating = review.get('rating')
        if rating is not None:
            try:
                validated['rating'] = float(rating)
                if validated['rating'] < 0 or validated['rating'] > 10:
                    validated['rating'] = None
            except (ValueError, TypeError):
                validated['rating'] = None
        
        # Date validation
        date_fields = ['date', 'created_at', 'updated_at']
        for date_field in date_fields:
            if date_field in review:
                validated[date_field] = review[date_field]
        
        # Copy other fields
        for field in ['author', 'title', 'helpful_votes', 'total_votes', 'url']:
            if field in review:
                validated[field] = review[field]
        
        return validated
    
    @staticmethod
    def is_valid_review(review: Dict, min_length: int = 50) -> bool:
        """Check if review meets quality criteria"""
        text = review.get('text', '')
        
        # Length check
        if len(text) < min_length:
            return False
        
        # Basic content check
        if not text.strip():
            return False
        
        # Check for spam indicators
        spam_patterns = [
            r'click here',
            r'visit our website',
            r'buy now',
            r'free download',
            r'www\.',
            r'http[s]?://'
        ]
        
        text_lower = text.lower()
        for pattern in spam_patterns:
            if re.search(pattern, text_lower):
                return False
        
        return True

def remove_duplicates(data: List[Dict], key_fields: List[str]) -> List[Dict]:
    """Remove duplicate records based on key fields"""
    seen = set()
    unique_data = []
    
    for item in data:
        # Create a key from specified fields
        key_values = tuple(str(item.get(field, '')) for field in key_fields)
        
        if key_values not in seen:
            seen.add(key_values)
            unique_data.append(item)
    
    logger.info(f"Removed {len(data) - len(unique_data)} duplicates")
    return unique_data

def standardize_date_format(date_str: str) -> Optional[str]:
    """Standardize date format to YYYY-MM-DD"""
    if not date_str:
        return None
    
    # Common date formats
    date_formats = [
        '%Y-%m-%d',
        '%d %B %Y',
        '%B %d, %Y',
        '%m/%d/%Y',
        '%d/%m/%Y',
        '%Y-%m-%dT%H:%M:%S.%fZ',
        '%Y-%m-%dT%H:%M:%SZ'
    ]
    
    for fmt in date_formats:
        try:
            date_obj = datetime.strptime(date_str.strip(), fmt)
            return date_obj.strftime('%Y-%m-%d')
        except ValueError:
            continue
    
    logger.warning(f"Could not parse date: {date_str}")
    return None
