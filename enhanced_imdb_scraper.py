#!/usr/bin/env python3
"""
Enhanced IMDB Scraper with Advanced Anti-Detection
Uses multiple techniques to bypass IMDB's anti-bot measures
"""

import sys
import time
import requests
import pandas as pd
import json
from pathlib import Path
from datetime import datetime
from bs4 import BeautifulSoup
import random
from urllib.parse import urljoin, quote
import re
from tqdm import tqdm
import cloudscraper
from fake_useragent import UserAgent

# Add project root to path
sys.path.append(str(Path(__file__).parent))

try:
    from config import RAW_DATA_DIR
except ImportError:
    RAW_DATA_DIR = Path("data/raw")

class EnhancedIMDBScraper:
    """Enhanced IMDB scraper with advanced anti-detection techniques"""
    
    def __init__(self):
        self.base_url = "https://www.imdb.com"
        
        # Try different scraping approaches
        self.scrapers = self.initialize_scrapers()
        self.current_scraper = 0
        
        # Ensure data directory exists
        RAW_DATA_DIR.mkdir(parents=True, exist_ok=True)
        
        print("🎭 Enhanced IMDB Scraper Initialized")
        print(f"Available scrapers: {len(self.scrapers)}")
        print(f"Data Directory: {RAW_DATA_DIR}")
    
    def initialize_scrapers(self):
        """Initialize multiple scraper instances with different configurations"""
        scrapers = []
        
        # 1. CloudScraper (bypasses Cloudflare)
        try:
            scraper1 = cloudscraper.create_scraper(
                browser={
                    'browser': 'chrome',
                    'platform': 'windows',
                    'mobile': False
                }
            )
            scrapers.append(('CloudScraper', scraper1))
            print("✅ CloudScraper initialized")
        except Exception as e:
            print(f"⚠️ CloudScraper failed: {str(e)}")
        
        # 2. Regular requests with rotating user agents
        try:
            ua = UserAgent()
            session = requests.Session()
            session.headers.update({
                'User-Agent': ua.random,
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            })
            scrapers.append(('UserAgent', session))
            print("✅ UserAgent scraper initialized")
        except Exception as e:
            print(f"⚠️ UserAgent scraper failed: {str(e)}")
        
        # 3. Basic requests session
        try:
            session = requests.Session()
            session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'none',
                'Cache-Control': 'max-age=0'
            })
            scrapers.append(('Basic', session))
            print("✅ Basic scraper initialized")
        except Exception as e:
            print(f"⚠️ Basic scraper failed: {str(e)}")
        
        return scrapers
    
    def make_request(self, url, retries=3):
        """Make request using available scrapers with fallback"""
        for scraper_name, scraper in self.scrapers:
            print(f"🔄 Trying {scraper_name} scraper...")
            
            for attempt in range(retries):
                try:
                    # Add random delay
                    time.sleep(random.uniform(2, 5))
                    
                    response = scraper.get(url, timeout=20)
                    
                    print(f"   Status: {response.status_code}")
                    
                    if response.status_code == 200:
                        print(f"✅ Success with {scraper_name}")
                        return response
                    elif response.status_code == 202:
                        print(f"⚠️ {scraper_name} got 202 (processing)")
                        continue
                    elif response.status_code in [403, 503]:
                        print(f"⚠️ {scraper_name} blocked ({response.status_code})")
                        break  # Try next scraper
                    else:
                        print(f"⚠️ {scraper_name} returned {response.status_code}")
                        
                except requests.exceptions.RequestException as e:
                    print(f"❌ {scraper_name} error: {str(e)}")
                    if attempt < retries - 1:
                        time.sleep(random.uniform(3, 7))
        
        return None
    
    def test_alternative_endpoints(self):
        """Test alternative IMDB endpoints that might be less protected"""
        print("\n🔍 Testing Alternative IMDB Endpoints...")
        print("=" * 50)
        
        # Alternative endpoints to try
        endpoints = [
            ("Mobile site", "https://m.imdb.com/"),
            ("API endpoint", "https://www.imdb.com/api/v1/"),
            ("RSS feed", "https://rss.imdb.com/"),
            ("Datasets", "https://datasets.imdbws.com/"),
            ("Title lookup", "https://www.imdb.com/title/tt0111161/"),
            ("Search API", "https://v2.sg.media-imdb.com/suggestion/"),
        ]
        
        successful_endpoints = []
        
        for name, url in endpoints:
            print(f"\n🔍 Testing: {name}")
            print(f"   URL: {url}")
            
            response = self.make_request(url, retries=1)
            
            if response and response.status_code == 200:
                print(f"✅ {name} accessible")
                successful_endpoints.append((name, url, response))
                
                # Check content
                if len(response.content) > 1000:
                    print(f"   Content size: {len(response.content)} bytes")
                    if "imdb" in response.text.lower():
                        print("   ✅ Contains IMDB content")
                    else:
                        print("   ⚠️ Content might be different")
                else:
                    print("   ⚠️ Very small response")
            else:
                print(f"❌ {name} not accessible")
        
        return successful_endpoints
    
    def test_imdb_datasets(self):
        """Test IMDB's official datasets (publicly available)"""
        print("\n📊 Testing IMDB Official Datasets...")
        print("=" * 50)
        
        # IMDB provides official datasets
        dataset_urls = [
            "https://datasets.imdbws.com/title.basics.tsv.gz",
            "https://datasets.imdbws.com/title.ratings.tsv.gz",
            "https://datasets.imdbws.com/name.basics.tsv.gz"
        ]
        
        accessible_datasets = []
        
        for url in dataset_urls:
            dataset_name = url.split('/')[-1]
            print(f"\n🔍 Testing: {dataset_name}")
            
            try:
                # Use simple requests for datasets
                response = requests.head(url, timeout=10)
                
                if response.status_code == 200:
                    print(f"✅ {dataset_name} accessible")
                    size = response.headers.get('content-length')
                    if size:
                        size_mb = int(size) / (1024 * 1024)
                        print(f"   Size: {size_mb:.1f} MB")
                    accessible_datasets.append(url)
                else:
                    print(f"❌ {dataset_name} not accessible ({response.status_code})")
                    
            except Exception as e:
                print(f"❌ Error testing {dataset_name}: {str(e)}")
        
        if accessible_datasets:
            print(f"\n✅ Found {len(accessible_datasets)} accessible datasets")
            print("💡 These datasets contain comprehensive movie information")
            print("💡 Consider using these as an alternative to web scraping")
        
        return accessible_datasets
    
    def test_proxy_approach(self):
        """Test using proxy services (demonstration only)"""
        print("\n🌐 Testing Proxy Approach...")
        print("=" * 50)
        
        print("💡 Proxy services can help bypass geo-restrictions and rate limits")
        print("💡 Popular proxy services include:")
        print("   - ProxyMesh")
        print("   - Bright Data (formerly Luminati)")
        print("   - ScrapingBee")
        print("   - Scrapfly")
        
        # For demonstration, we'll test a free proxy list service
        try:
            proxy_test_url = "https://httpbin.org/ip"
            response = requests.get(proxy_test_url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Current IP: {data.get('origin', 'Unknown')}")
                print("💡 Proxy rotation could help with IMDB access")
            else:
                print("⚠️ Cannot determine current IP")
                
        except Exception as e:
            print(f"❌ Proxy test error: {str(e)}")
    
    def suggest_alternatives(self):
        """Suggest alternative approaches for movie data collection"""
        print("\n💡 Alternative Data Collection Approaches...")
        print("=" * 50)
        
        alternatives = [
            {
                'name': 'TMDB API',
                'description': 'The Movie Database API (already working)',
                'pros': ['Official API', 'Comprehensive data', 'Free tier available'],
                'cons': ['Limited reviews compared to IMDB'],
                'status': '✅ WORKING'
            },
            {
                'name': 'OMDB API',
                'description': 'Open Movie Database API',
                'pros': ['IMDB data via API', 'Easy to use'],
                'cons': ['Limited free requests', 'Requires API key'],
                'status': '🔄 TESTABLE'
            },
            {
                'name': 'IMDB Datasets',
                'description': 'Official IMDB datasets (TSV files)',
                'pros': ['Official data', 'Comprehensive', 'Free'],
                'cons': ['No reviews', 'Bulk download only'],
                'status': '✅ AVAILABLE'
            },
            {
                'name': 'Rotten Tomatoes',
                'description': 'Alternative review source',
                'pros': ['Rich review data', 'Professional critics'],
                'cons': ['Different rating system', 'Anti-scraping measures'],
                'status': '🔄 TESTABLE'
            },
            {
                'name': 'Metacritic',
                'description': 'Aggregated review scores',
                'pros': ['Professional reviews', 'User reviews'],
                'cons': ['Limited data', 'Scraping challenges'],
                'status': '🔄 TESTABLE'
            }
        ]
        
        for alt in alternatives:
            print(f"\n{alt['status']} {alt['name']}")
            print(f"   Description: {alt['description']}")
            print(f"   Pros: {', '.join(alt['pros'])}")
            print(f"   Cons: {', '.join(alt['cons'])}")
    
    def test_omdb_api(self):
        """Test OMDB API as an alternative"""
        print("\n🎬 Testing OMDB API...")
        print("=" * 50)
        
        # OMDB API endpoint
        omdb_url = "http://www.omdbapi.com/"
        
        # Test without API key first
        test_params = {
            'i': 'tt0111161',  # Shawshank Redemption
            't': '',
            'plot': 'short',
            'r': 'json'
        }
        
        try:
            response = requests.get(omdb_url, params=test_params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'Error' in data:
                    print(f"⚠️ OMDB API requires key: {data['Error']}")
                    print("💡 Get free API key from: http://www.omdbapi.com/apikey.aspx")
                else:
                    print("✅ OMDB API accessible")
                    print(f"   Title: {data.get('Title', 'Unknown')}")
                    print(f"   Year: {data.get('Year', 'Unknown')}")
                    print(f"   IMDB Rating: {data.get('imdbRating', 'Unknown')}")
            else:
                print(f"❌ OMDB API not accessible ({response.status_code})")
                
        except Exception as e:
            print(f"❌ OMDB API error: {str(e)}")
    
    def run_comprehensive_analysis(self):
        """Run comprehensive analysis of IMDB scraping alternatives"""
        print("🔬 IMDB Scraping Comprehensive Analysis")
        print("=" * 60)
        print(f"Analysis started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        results = {}
        
        # Test 1: Alternative endpoints
        print("\n" + "="*60)
        accessible_endpoints = self.test_alternative_endpoints()
        results['alternative_endpoints'] = len(accessible_endpoints) > 0
        
        # Test 2: Official datasets
        print("\n" + "="*60)
        accessible_datasets = self.test_imdb_datasets()
        results['official_datasets'] = len(accessible_datasets) > 0
        
        # Test 3: Proxy approach (informational)
        print("\n" + "="*60)
        self.test_proxy_approach()
        
        # Test 4: OMDB API
        print("\n" + "="*60)
        self.test_omdb_api()
        
        # Test 5: Suggest alternatives
        print("\n" + "="*60)
        self.suggest_alternatives()
        
        # Summary and recommendations
        print("\n" + "=" * 60)
        print("🏁 Analysis Summary & Recommendations")
        print("=" * 60)
        
        if results['official_datasets']:
            print("✅ RECOMMENDED: Use IMDB Official Datasets")
            print("   - Download: https://datasets.imdbws.com/")
            print("   - Contains: Movie info, ratings, cast, crew")
            print("   - Missing: User reviews (use TMDB for reviews)")
        
        if accessible_endpoints:
            print("✅ ALTERNATIVE: Some IMDB endpoints accessible")
            print("   - May require careful implementation")
            print("   - Consider rate limiting and respectful scraping")
        
        print("\n💡 BEST STRATEGY:")
        print("1. Use TMDB API for movies + reviews (already working)")
        print("2. Supplement with IMDB datasets for additional metadata")
        print("3. Consider OMDB API for IMDB ratings")
        print("4. Implement proxy rotation if needed for scale")
        
        # Save analysis results
        self.save_analysis_results(results, accessible_endpoints, accessible_datasets)
        
        return results
    
    def save_analysis_results(self, results, endpoints, datasets):
        """Save analysis results to file"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        analysis_data = {
            'analysis_timestamp': datetime.now().isoformat(),
            'test_results': results,
            'accessible_endpoints': [{'name': name, 'url': url} for name, url, _ in endpoints],
            'accessible_datasets': datasets,
            'recommendations': {
                'primary_strategy': 'TMDB API + IMDB Datasets',
                'alternative_apis': ['OMDB', 'Rotten Tomatoes'],
                'scraping_feasibility': 'Limited due to anti-bot measures',
                'best_practices': [
                    'Use official APIs when available',
                    'Respect rate limits and robots.txt',
                    'Consider proxy rotation for scale',
                    'Implement proper error handling'
                ]
            }
        }
        
        results_file = RAW_DATA_DIR / f"imdb_analysis_{timestamp}.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(analysis_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n💾 Analysis results saved to: {results_file}")

def main():
    """Main execution function"""
    scraper = EnhancedIMDBScraper()
    scraper.run_comprehensive_analysis()

if __name__ == "__main__":
    main()
