{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Data Collection - IMDB/TMDB Movie Data and Reviews\n", "\n", "This notebook demonstrates the data collection process for 2025 movies and their reviews from IMDB and TMDB."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import json\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from pathlib import Path\n", "import sys\n", "from tqdm import tqdm\n", "import time\n", "\n", "# Add project root to path\n", "project_root = Path.cwd().parent\n", "sys.path.append(str(project_root))\n", "\n", "# Import project modules\n", "from config import RAW_DATA_DIR, TARGET_YEAR\n", "from 01_data_collection.01_imdb_scraper import IMDBScraper\n", "from 01_data_collection.02_tmdb_scraper import TMDBScraper\n", "from 01_data_collection.03_review_scraper import ReviewScraper\n", "\n", "print(\"Libraries and modules imported successfully!\")\n", "print(f\"Target year for data collection: {TARGET_YEAR}\")\n", "print(f\"Data will be saved to: {RAW_DATA_DIR}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. TMDB Data Collection\n", "\n", "First, let's collect movie data from TMDB API (The Movie Database)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize TMDB scraper\n", "tmdb_scraper = TMDBScraper()\n", "\n", "# Check API key configuration\n", "if tmdb_scraper.api_key and tmdb_scraper.api_key != 'your_tmdb_api_key_here':\n", "    print(\"✅ TMDB API key configured\")\n", "    api_configured = True\n", "else:\n", "    print(\"⚠️ TMDB API key not configured. Please set TMDB_API_KEY in .env file\")\n", "    print(\"You can get a free API key from: https://www.themoviedb.org/settings/api\")\n", "    api_configured = False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Collect TMDB data (only if API key is configured)\n", "if api_configured:\n", "    print(f\"🎬 Starting TMDB data collection for {TARGET_YEAR} movies...\")\n", "    \n", "    try:\n", "        # Discover movies\n", "        print(\"📡 Discovering movies from TMDB...\")\n", "        movies_basic = tmdb_scraper.discover_movies_by_year(TARGET_YEAR)\n", "        print(f\"Found {len(movies_basic)} movies\")\n", "        \n", "        # Show sample of discovered movies\n", "        if movies_basic:\n", "            sample_df = pd.DataFrame(movies_basic[:5])\n", "            print(\"\\nSample of discovered movies:\")\n", "            display(sample_df[['title', 'release_date', 'vote_average', 'popularity']].head())\n", "        \n", "        # Enrich with detailed data (limit to first 20 for demo)\n", "        print(\"\\n🔍 Enriching with detailed movie data...\")\n", "        movies_sample = movies_basic[:20]  # Limit for demo\n", "        enriched_movies = tmdb_scraper.enrich_movie_data(movies_sample)\n", "        \n", "        print(f\"✅ Enriched {len(enriched_movies)} movies with detailed data\")\n", "        \n", "        # Save data\n", "        tmdb_scraper.save_movies_data(enriched_movies, 'tmdb_movies_sample.json')\n", "        \n", "        tmdb_movies = enriched_movies\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ Error collecting TMDB data: {str(e)}\")\n", "        tmdb_movies = []\nelse:\n", "    print(\"⏭️ Skipping TMDB data collection (API key not configured)\")\n", "    tmdb_movies = []"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. IMDB Data Collection\n", "\n", "Now let's collect additional data from IMDB through web scraping."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize IMDB scraper\n", "imdb_scraper = IMDBScraper()\n", "\n", "print(\"🎭 Starting IMDB data collection...\")\n", "print(\"Note: This is a demo with limited data to avoid overwhelming IMDB servers\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Search for movies on IMDB\n", "try:\n", "    print(f\"🔍 Searching for {TARGET_YEAR} movies on IMDB...\")\n", "    imdb_movies_basic = imdb_scraper.search_movies_by_year(TARGET_YEAR)\n", "    \n", "    if imdb_movies_basic:\n", "        print(f\"Found {len(imdb_movies_basic)} movies on IMDB\")\n", "        \n", "        # Show sample\n", "        sample_df = pd.DataFrame(imdb_movies_basic[:5])\n", "        print(\"\\nSample of IMDB movies:\")\n", "        display(sample_df[['title', 'year', 'rating', 'genres']].head())\n", "        \n", "        # Enrich with detailed data (limit to first 5 for demo)\n", "        print(\"\\n📝 Enriching with detailed IMDB data (limited sample)...\")\n", "        movies_sample = imdb_movies_basic[:5]  # Very limited for demo\n", "        enriched_imdb = imdb_scraper.enrich_movie_data(movies_sample)\n", "        \n", "        print(f\"✅ Enriched {len(enriched_imdb)} movies with IMDB details\")\n", "        \n", "        # Save data\n", "        imdb_scraper.save_movies_data(enriched_imdb, 'imdb_movies_sample.json')\n", "        \n", "        imdb_movies = enriched_imdb\n", "    else:\n", "        print(\"No movies found on IMDB\")\n", "        imdb_movies = []\n", "        \nexcept Exception as e:\n", "    print(f\"❌ Error collecting IMDB data: {str(e)}\")\n", "    print(\"This might be due to IMDB's anti-scraping measures or network issues\")\n", "    imdb_movies = []"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Review Collection\n", "\n", "Now let's collect reviews for the movies we've gathered."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Combine movie data from both sources\n", "all_movies = []\n", "\n", "if tmdb_movies:\n", "    all_movies.extend(tmdb_movies)\n", "    print(f\"Added {len(tmdb_movies)} TMDB movies\")\n", "\n", "if imdb_movies:\n", "    all_movies.extend(imdb_movies)\n", "    print(f\"Added {len(imdb_movies)} IMDB movies\")\n", "\n", "print(f\"\\nTotal movies for review collection: {len(all_movies)}\")\n", "\n", "if all_movies:\n", "    # Show combined data sample\n", "    movies_df = pd.DataFrame(all_movies)\n", "    print(\"\\nCombined movie data sample:\")\n", "    display(movies_df[['title', 'year']].head(10))\nelse:\n", "    print(\"⚠️ No movie data available for review collection\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Collect reviews\n", "if all_movies:\n", "    review_scraper = ReviewScraper()\n", "    \n", "    print(\"📝 Starting review collection...\")\n", "    print(\"Note: This is a demo with limited reviews to avoid overwhelming servers\")\n", "    \n", "    try:\n", "        # Limit to first few movies for demo\n", "        movies_for_reviews = all_movies[:3]\n", "        print(f\"Collecting reviews for {len(movies_for_reviews)} movies (demo)\")\n", "        \n", "        all_reviews = review_scraper.collect_reviews(movies_for_reviews)\n", "        \n", "        if all_reviews:\n", "            print(f\"✅ Collected {len(all_reviews)} reviews\")\n", "            \n", "            # Show review sample\n", "            reviews_df = pd.DataFrame(all_reviews)\n", "            print(\"\\nReview data sample:\")\n", "            display(reviews_df[['source', 'rating', 'text']].head())\n", "            \n", "            # Show review statistics\n", "            print(\"\\nReview Statistics:\")\n", "            print(f\"Total reviews: {len(all_reviews)}\")\n", "            print(f\"Sources: {reviews_df['source'].value_counts().to_dict()}\")\n", "            if 'rating' in reviews_df.columns:\n", "                print(f\"Average rating: {reviews_df['rating'].mean():.2f}\")\n", "            print(f\"Average review length: {reviews_df['text'].str.len().mean():.0f} characters\")\n", "        else:\n", "            print(\"No reviews collected\")\n", "            \n", "    except Exception as e:\n", "        print(f\"❌ Error collecting reviews: {str(e)}\")\n", "        all_reviews = []\nelse:\n", "    print(\"⏭️ Skipping review collection (no movie data available)\")\n", "    all_reviews = []"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Data Analysis and Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Analyze collected data\n", "if all_movies:\n", "    print(\"📊 Analyzing collected movie data...\")\n", "    \n", "    movies_df = pd.DataFrame(all_movies)\n", "    \n", "    # Basic statistics\n", "    print(f\"\\nMovie Data Summary:\")\n", "    print(f\"Total movies: {len(movies_df)}\")\n", "    print(f\"Columns: {list(movies_df.columns)}\")\n", "    \n", "    # Rating analysis\n", "    rating_cols = ['rating', 'vote_average', 'imdb_rating']\n", "    for col in rating_cols:\n", "        if col in movies_df.columns:\n", "            valid_ratings = movies_df[col].dropna()\n", "            if len(valid_ratings) > 0:\n", "                print(f\"\\n{col} statistics:\")\n", "                print(f\"  Count: {len(valid_ratings)}\")\n", "                print(f\"  Mean: {valid_ratings.mean():.2f}\")\n", "                print(f\"  Range: {valid_ratings.min():.1f} - {valid_ratings.max():.1f}\")\n", "            break\n", "    \n", "    # Genre analysis\n", "    if 'genres' in movies_df.columns:\n", "        all_genres = []\n", "        for genres in movies_df['genres'].dropna():\n", "            if isinstance(genres, list):\n", "                all_genres.extend(genres)\n", "        \n", "        if all_genres:\n", "            genre_counts = pd.Series(all_genres).value_counts()\n", "            print(f\"\\nTop genres: {dict(genre_counts.head())}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Visualize collected data\n", "if all_movies and len(all_movies) > 1:\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "    fig.suptitle('Collected Movie Data Analysis', fontsize=16)\n", "    \n", "    movies_df = pd.DataFrame(all_movies)\n", "    \n", "    # 1. Rating distribution\n", "    rating_cols = ['rating', 'vote_average', 'imdb_rating']\n", "    for col in rating_cols:\n", "        if col in movies_df.columns:\n", "            valid_ratings = movies_df[col].dropna()\n", "            if len(valid_ratings) > 0:\n", "                axes[0, 0].hist(valid_ratings, bins=10, alpha=0.7, edgecolor='black')\n", "                axes[0, 0].set_title(f'{col.title()} Distribution')\n", "                axes[0, 0].set_xlabel('Rating')\n", "                axes[0, 0].set_ylabel('Frequency')\n", "            break\n", "    \n", "    # 2. Genre distribution\n", "    if 'genres' in movies_df.columns:\n", "        all_genres = []\n", "        for genres in movies_df['genres'].dropna():\n", "            if isinstance(genres, list):\n", "                all_genres.extend(genres)\n", "        \n", "        if all_genres:\n", "            genre_counts = pd.Series(all_genres).value_counts().head(8)\n", "            axes[0, 1].bar(range(len(genre_counts)), genre_counts.values)\n", "            axes[0, 1].set_title('Top Genres')\n", "            axes[0, 1].set_xlabel('Genre')\n", "            axes[0, 1].set_ylabel('Count')\n", "            axes[0, 1].set_xticks(range(len(genre_counts)))\n", "            axes[0, 1].set_xticklabels(genre_counts.index, rotation=45)\n", "    \n", "    # 3. Data sources\n", "    sources = []\n", "    for movie in all_movies:\n", "        if movie.get('tmdb_id') or 'tmdb' in str(movie):\n", "            sources.append('TMDB')\n", "        elif movie.get('imdb_id'):\n", "            sources.append('IMDB')\n", "        else:\n", "            sources.append('Unknown')\n", "    \n", "    source_counts = pd.Series(sources).value_counts()\n", "    axes[1, 0].pie(source_counts.values, labels=source_counts.index, autopct='%1.1f%%')\n", "    axes[1, 0].set_title('Data Sources')\n", "    \n", "    # 4. Review statistics\n", "    if all_reviews:\n", "        reviews_df = pd.DataFrame(all_reviews)\n", "        if 'source' in reviews_df.columns:\n", "            review_sources = reviews_df['source'].value_counts()\n", "            axes[1, 1].bar(review_sources.index, review_sources.values)\n", "            axes[1, 1].set_title('Reviews by Source')\n", "            axes[1, 1].set_xlabel('Source')\n", "            axes[1, 1].set_ylabel('Count')\n", "    else:\n", "        axes[1, 1].text(0.5, 0.5, 'No review data', ha='center', va='center')\n", "        axes[1, 1].set_title('Reviews')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\nelse:\n", "    print(\"📊 Insufficient data for visualization\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Data Quality Assessment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Assess data quality\n", "print(\"🔍 Data Quality Assessment\")\n", "print(\"=\" * 30)\n", "\n", "if all_movies:\n", "    movies_df = pd.DataFrame(all_movies)\n", "    \n", "    print(\"\\nMovie Data Quality:\")\n", "    print(f\"Total movies: {len(movies_df)}\")\n", "    print(f\"Missing titles: {movies_df['title'].isna().sum()}\")\n", "    \n", "    # Check for required fields\n", "    required_fields = ['title', 'year']\n", "    for field in required_fields:\n", "        if field in movies_df.columns:\n", "            missing = movies_df[field].isna().sum()\n", "            print(f\"Missing {field}: {missing} ({missing/len(movies_df)*100:.1f}%)\")\n", "    \n", "    # Check data completeness\n", "    optional_fields = ['rating', 'genres', 'overview', 'plot']\n", "    print(\"\\nData Completeness:\")\n", "    for field in optional_fields:\n", "        if field in movies_df.columns:\n", "            present = movies_df[field].notna().sum()\n", "            print(f\"{field}: {present}/{len(movies_df)} ({present/len(movies_df)*100:.1f}%)\")\n", "\n", "if all_reviews:\n", "    reviews_df = pd.DataFrame(all_reviews)\n", "    \n", "    print(\"\\nReview Data Quality:\")\n", "    print(f\"Total reviews: {len(reviews_df)}\")\n", "    \n", "    # Text length analysis\n", "    if 'text' in reviews_df.columns:\n", "        text_lengths = reviews_df['text'].str.len()\n", "        print(f\"Average text length: {text_lengths.mean():.0f} characters\")\n", "        print(f\"Short reviews (<50 chars): {(text_lengths < 50).sum()}\")\n", "        print(f\"Long reviews (>1000 chars): {(text_lengths > 1000).sum()}\")\n", "    \n", "    # Rating completeness\n", "    if 'rating' in reviews_df.columns:\n", "        rated_reviews = reviews_df['rating'].notna().sum()\n", "        print(f\"Reviews with ratings: {rated_reviews}/{len(reviews_df)} ({rated_reviews/len(reviews_df)*100:.1f}%)\")\n", "\n", "print(\"\\n✅ Data quality assessment complete\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. <PERSON> Collected Data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Save final collected data\n", "print(\"💾 Saving collected data...\")\n", "\n", "if all_movies:\n", "    # Save movies\n", "    movies_file = RAW_DATA_DIR / 'collected_movies.json'\n", "    with open(movies_file, 'w', encoding='utf-8') as f:\n", "        json.dump(all_movies, f, indent=2, ensure_ascii=False)\n", "    \n", "    # Save as CSV too\n", "    movies_df = pd.DataFrame(all_movies)\n", "    movies_csv = RAW_DATA_DIR / 'collected_movies.csv'\n", "    movies_df.to_csv(movies_csv, index=False, encoding='utf-8')\n", "    \n", "    print(f\"✅ Saved {len(all_movies)} movies to {movies_file}\")\n", "\n", "if all_reviews:\n", "    # Save reviews\n", "    reviews_file = RAW_DATA_DIR / 'collected_reviews.json'\n", "    with open(reviews_file, 'w', encoding='utf-8') as f:\n", "        json.dump(all_reviews, f, indent=2, ensure_ascii=False)\n", "    \n", "    # Save as CSV too\n", "    reviews_df = pd.DataFrame(all_reviews)\n", "    reviews_csv = RAW_DATA_DIR / 'collected_reviews.csv'\n", "    reviews_df.to_csv(reviews_csv, index=False, encoding='utf-8')\n", "    \n", "    print(f\"✅ Saved {len(all_reviews)} reviews to {reviews_file}\")\n", "\n", "print(\"\\n🎉 Data collection notebook complete!\")\n", "print(\"\\nNext steps:\")\n", "print(\"1. Run data cleaning notebook (03_data_cleaning.ipynb)\")\n", "print(\"2. Perform exploratory data analysis\")\n", "print(\"3. Extract aspects and analyze sentiment\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Summary\n", "\n", "This notebook demonstrated:\n", "\n", "1. **TMDB Data Collection**: Using the TMDB API to gather comprehensive movie data\n", "2. **IMDB Data Collection**: Web scraping IMDB for additional movie information\n", "3. **Review Collection**: Gathering movie reviews from multiple sources\n", "4. **Data Quality Assessment**: Analyzing the completeness and quality of collected data\n", "5. **Data Visualization**: Creating charts to understand the collected data\n", "6. **Data Storage**: Saving collected data in both JSON and CSV formats\n", "\n", "The collected data serves as the foundation for the subsequent analysis steps in the pipeline."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}