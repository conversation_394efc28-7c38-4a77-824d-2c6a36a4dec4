# Jupyter Notebooks for IMDB Aspect-Based Sentiment Analysis

This directory contains comprehensive <PERSON><PERSON><PERSON> notebooks for interactive analysis and exploration of the IMDB aspect-based sentiment analysis project.

## 📓 Available Notebooks

### 1. **01_data_exploration.ipynb** ✅
**Purpose**: Initial data exploration and understanding
- Load and examine raw movie and review data
- Basic statistical analysis
- Data quality assessment
- Visualization of data distributions
- Identify patterns and potential issues

**Key Features**:
- Data loading from multiple sources
- Statistical summaries
- Distribution plots
- Missing value analysis
- Sample data inspection

### 2. **02_data_collection.ipynb** ✅
**Purpose**: Interactive data collection from TMDB and IMDB
- TMDB API data collection demonstration
- IMDB web scraping examples
- Review collection from multiple sources
- Data quality assessment during collection
- Real-time collection monitoring

**Key Features**:
- API integration examples
- Web scraping demonstrations
- Rate limiting and error handling
- Data validation during collection
- Progress tracking and visualization

### 3. **03_data_cleaning.ipynb** ✅
**Purpose**: Comprehensive data cleaning and preprocessing
- Text cleaning and normalization
- Data validation and quality checks
- Duplicate detection and removal
- Text preprocessing for NLP
- Data quality visualization

**Key Features**:
- Step-by-step text cleaning demonstration
- Before/after comparisons
- Quality metrics calculation
- Duplicate analysis
- Cleaned data export

### 4. **04_eda_analysis.ipynb** 🚧
**Purpose**: Exploratory Data Analysis (EDA)
- Statistical analysis of cleaned data
- Distribution analysis
- Correlation studies
- Pattern identification
- Interactive visualizations

**Planned Features**:
- Comprehensive statistical summaries
- Interactive plots with Plotly
- Genre analysis
- Rating distributions
- Review length analysis
- Temporal patterns

### 5. **05_aspect_extraction.ipynb** ✅
**Purpose**: Movie aspect extraction from reviews
- Aspect extraction demonstration
- Keyword-based and pattern-based extraction
- Co-occurrence analysis
- Aspect distribution visualization
- Network analysis of aspect relationships

**Key Features**:
- Interactive aspect extraction demo
- Bulk processing of reviews
- Co-occurrence network visualization
- Aspect frequency analysis
- Word clouds and distribution plots

### 6. **06_sentiment_analysis.ipynb** 🚧
**Purpose**: Aspect-based sentiment analysis
- Sentiment analysis on extracted aspects
- Multiple sentiment models comparison
- Aspect-level sentiment visualization
- Movie-level sentiment aggregation
- Comprehensive reporting

**Planned Features**:
- VADER and TextBlob sentiment analysis
- Aspect-specific sentiment scores
- Sentiment distribution by aspects
- Movie ranking by aspect sentiment
- Interactive sentiment dashboard

### 7. **07_results_visualization.ipynb** 🚧
**Purpose**: Final results and comprehensive visualization
- Interactive dashboard creation
- Comprehensive result analysis
- Export functionality
- Report generation
- Publication-ready visualizations

**Planned Features**:
- Interactive Plotly dashboards
- Comprehensive result summaries
- Export to various formats
- Statistical significance testing
- Publication-ready plots

## 🚀 How to Use the Notebooks

### Prerequisites
```bash
# Install required packages
pip install -r requirements.txt

# Download NLTK data
python -c "import nltk; nltk.download('punkt'); nltk.download('stopwords'); nltk.download('wordnet')"

# Download spaCy model
python -m spacy download en_core_web_sm
```

### Running the Notebooks

1. **Start Jupyter Lab/Notebook**:
   ```bash
   jupyter lab
   # or
   jupyter notebook
   ```

2. **Run notebooks in order**:
   - Start with `01_data_exploration.ipynb` to understand the data
   - Use `02_data_collection.ipynb` to collect new data (optional)
   - Process data with `03_data_cleaning.ipynb`
   - Explore with `04_eda_analysis.ipynb`
   - Extract aspects with `05_aspect_extraction.ipynb`
   - Analyze sentiment with `06_sentiment_analysis.ipynb`
   - Visualize results with `07_results_visualization.ipynb`

3. **Interactive Features**:
   - All notebooks include interactive widgets where applicable
   - Plotly visualizations for better interactivity
   - Progress bars for long-running operations
   - Real-time data quality monitoring

## 📊 Notebook Features

### Common Features Across All Notebooks:
- **Error Handling**: Robust error handling with fallback sample data
- **Progress Tracking**: Progress bars for long operations
- **Interactive Visualizations**: Plotly charts for better exploration
- **Data Validation**: Quality checks at each step
- **Export Functionality**: Save results in multiple formats
- **Documentation**: Comprehensive markdown explanations

### Visualization Types:
- **Static Plots**: Matplotlib and Seaborn for publication-ready charts
- **Interactive Plots**: Plotly for exploration and interaction
- **Network Graphs**: NetworkX for aspect relationships
- **Word Clouds**: Visual representation of text data
- **Statistical Plots**: Distribution, correlation, and trend analysis

## 🔧 Customization

### Modifying Analysis Parameters:
```python
# In any notebook, you can modify these parameters:
TARGET_YEAR = 2025  # Change target year
MIN_REVIEW_LENGTH = 50  # Minimum review length
SIMILARITY_THRESHOLD = 0.85  # Duplicate detection threshold
MOVIE_ASPECTS = ['plot', 'acting', 'direction']  # Target aspects
```

### Adding Custom Visualizations:
```python
# Example: Add custom plot
def create_custom_plot(data):
    fig = px.scatter(data, x='rating', y='sentiment_score')
    fig.show()
```

### Extending Analysis:
- Add new aspect categories
- Implement custom sentiment models
- Create additional visualizations
- Export results in different formats

## 📁 Output Files

Each notebook generates specific output files:

- **01_data_exploration.ipynb**: Exploration reports and initial insights
- **02_data_collection.ipynb**: Raw collected data files
- **03_data_cleaning.ipynb**: Cleaned and processed data
- **04_eda_analysis.ipynb**: Statistical analysis reports
- **05_aspect_extraction.ipynb**: Reviews with extracted aspects
- **06_sentiment_analysis.ipynb**: Sentiment analysis results
- **07_results_visualization.ipynb**: Final reports and visualizations

## 🎯 Learning Objectives

By working through these notebooks, you will learn:

1. **Data Collection**: API integration and web scraping techniques
2. **Data Cleaning**: Text preprocessing and quality assurance
3. **EDA**: Statistical analysis and pattern recognition
4. **NLP**: Aspect extraction and sentiment analysis
5. **Visualization**: Creating compelling data visualizations
6. **Project Management**: Organizing a complete data science project

## 🔍 Troubleshooting

### Common Issues:

1. **Missing Data**: Notebooks include sample data for demonstration
2. **API Keys**: Set TMDB_API_KEY in .env file for data collection
3. **Dependencies**: Install all requirements from requirements.txt
4. **Memory Issues**: Process data in smaller batches if needed

### Getting Help:

- Check the USAGE.md file for detailed instructions
- Review the main README.md for project overview
- Examine the code comments for specific functionality
- Run the test suite to verify installation

## 📈 Next Steps

After completing the notebooks:

1. **Run the Full Pipeline**: Use `main.py` to run the complete analysis
2. **Launch Dashboard**: Use the Streamlit dashboard for interactive exploration
3. **Customize Analysis**: Modify parameters for your specific needs
4. **Extend Functionality**: Add new features or analysis methods

---

**Note**: Notebooks marked with ✅ are complete and ready to use. Notebooks marked with 🚧 are planned for future implementation or may need additional development based on your specific requirements.
