"""
Aspect Extraction Module
Extracts movie aspects from reviews for aspect-based sentiment analysis
"""

import pandas as pd
import numpy as np
import json
import logging
from typing import List, Dict, Optional, Tuple
from pathlib import Path
import sys
import re
import nltk
from collections import Counter, defaultdict

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from config import MOVIE_ASPECTS, ASPECT_KEYWORDS, PROCESSED_DATA_DIR

logger = logging.getLogger(__name__)

class AspectExtractor:
    """Extract movie aspects from review text"""
    
    def __init__(self):
        self.aspects = MOVIE_ASPECTS
        self.aspect_keywords = ASPECT_KEYWORDS
        self.extracted_aspects = []
        
        # Initialize NLTK components
        try:
            nltk.download('punkt', quiet=True)
            nltk.download('averaged_perceptron_tagger', quiet=True)
            nltk.download('wordnet', quiet=True)
        except Exception as e:
            logger.warning(f"Error downloading NLTK data: {str(e)}")
    
    def extract_aspects_from_text(self, text: str) -> Dict[str, List[str]]:
        """Extract aspects mentioned in review text"""
        if not text:
            return {}
        
        text_lower = text.lower()
        found_aspects = defaultdict(list)
        
        # Keyword-based extraction
        for aspect, keywords in self.aspect_keywords.items():
            for keyword in keywords:
                if keyword.lower() in text_lower:
                    # Find sentences containing the keyword
                    sentences = self._find_sentences_with_keyword(text, keyword)
                    found_aspects[aspect].extend(sentences)
        
        # Pattern-based extraction
        pattern_aspects = self._extract_aspects_by_patterns(text)
        for aspect, sentences in pattern_aspects.items():
            found_aspects[aspect].extend(sentences)
        
        # Remove duplicates
        for aspect in found_aspects:
            found_aspects[aspect] = list(set(found_aspects[aspect]))
        
        return dict(found_aspects)
    
    def _find_sentences_with_keyword(self, text: str, keyword: str) -> List[str]:
        """Find sentences containing specific keyword"""
        sentences = re.split(r'[.!?]+', text)
        matching_sentences = []
        
        for sentence in sentences:
            if keyword.lower() in sentence.lower():
                matching_sentences.append(sentence.strip())
        
        return matching_sentences
    
    def _extract_aspects_by_patterns(self, text: str) -> Dict[str, List[str]]:
        """Extract aspects using linguistic patterns"""
        aspects = defaultdict(list)
        
        # Pattern 1: "The [aspect] was [adjective]"
        pattern1 = r'the\s+(\w+)\s+(?:was|is|were|are)\s+(\w+)'
        matches = re.finditer(pattern1, text.lower())
        
        for match in matches:
            noun = match.group(1)
            adjective = match.group(2)
            
            # Map noun to aspect
            aspect = self._map_noun_to_aspect(noun)
            if aspect:
                sentence = match.group(0)
                aspects[aspect].append(sentence)
        
        # Pattern 2: "[adjective] [aspect]"
        pattern2 = r'(\w+)\s+(acting|plot|direction|music|effects|dialogue|characters|cinematography)'
        matches = re.finditer(pattern2, text.lower())
        
        for match in matches:
            adjective = match.group(1)
            aspect_word = match.group(2)
            
            aspect = self._map_noun_to_aspect(aspect_word)
            if aspect:
                sentence = match.group(0)
                aspects[aspect].append(sentence)
        
        return dict(aspects)
    
    def _map_noun_to_aspect(self, noun: str) -> Optional[str]:
        """Map extracted noun to predefined aspect"""
        noun_lower = noun.lower()
        
        # Direct mapping
        direct_mapping = {
            'acting': 'acting',
            'performance': 'acting',
            'actor': 'acting',
            'actress': 'acting',
            'cast': 'acting',
            'plot': 'plot',
            'story': 'plot',
            'storyline': 'plot',
            'script': 'plot',
            'direction': 'direction',
            'director': 'direction',
            'directing': 'direction',
            'cinematography': 'cinematography',
            'camera': 'cinematography',
            'visual': 'cinematography',
            'music': 'music',
            'soundtrack': 'music',
            'score': 'music',
            'sound': 'music',
            'dialogue': 'dialogue',
            'conversation': 'dialogue',
            'lines': 'dialogue',
            'character': 'characters',
            'characters': 'characters',
            'effects': 'effects',
            'cgi': 'effects',
            'pacing': 'pacing',
            'pace': 'pacing',
            'rhythm': 'pacing'
        }
        
        return direct_mapping.get(noun_lower)
    
    def extract_aspects_from_reviews(self, reviews_data: List[Dict]) -> List[Dict]:
        """Extract aspects from all reviews"""
        logger.info(f"Extracting aspects from {len(reviews_data)} reviews")
        
        enriched_reviews = []
        aspect_statistics = defaultdict(int)
        
        for i, review in enumerate(reviews_data):
            if i % 100 == 0:
                logger.info(f"Processing review {i+1}/{len(reviews_data)}")
            
            try:
                # Get review text
                text = review.get('text', '') or review.get('processed_text', '')
                
                if not text:
                    continue
                
                # Extract aspects
                aspects = self.extract_aspects_from_text(text)
                
                # Create enriched review
                enriched_review = review.copy()
                enriched_review['extracted_aspects'] = aspects
                enriched_review['aspect_count'] = len(aspects)
                enriched_review['mentioned_aspects'] = list(aspects.keys())
                
                # Update statistics
                for aspect in aspects.keys():
                    aspect_statistics[aspect] += 1
                
                enriched_reviews.append(enriched_review)
                
            except Exception as e:
                logger.warning(f"Error processing review {i}: {str(e)}")
                continue
        
        # Save statistics
        self._save_aspect_statistics(aspect_statistics, len(reviews_data))
        
        logger.info(f"Aspect extraction completed. Processed {len(enriched_reviews)} reviews")
        return enriched_reviews
    
    def _save_aspect_statistics(self, aspect_stats: Dict[str, int], total_reviews: int):
        """Save aspect extraction statistics"""
        statistics = {
            'total_reviews': total_reviews,
            'reviews_with_aspects': sum(1 for count in aspect_stats.values() if count > 0),
            'aspect_frequencies': dict(aspect_stats),
            'aspect_percentages': {
                aspect: (count / total_reviews) * 100 
                for aspect, count in aspect_stats.items()
            },
            'most_mentioned_aspects': sorted(
                aspect_stats.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:10]
        }
        
        # Save to file
        filepath = PROCESSED_DATA_DIR / 'aspect_extraction_stats.json'
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(statistics, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Aspect statistics saved to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving aspect statistics: {str(e)}")
    
    def analyze_aspect_cooccurrence(self, reviews_data: List[Dict]) -> Dict:
        """Analyze which aspects are mentioned together"""
        logger.info("Analyzing aspect co-occurrence")
        
        cooccurrence_matrix = defaultdict(lambda: defaultdict(int))
        aspect_pairs = defaultdict(int)
        
        for review in reviews_data:
            aspects = review.get('mentioned_aspects', [])
            
            # Count co-occurrences
            for i, aspect1 in enumerate(aspects):
                for j, aspect2 in enumerate(aspects):
                    if i != j:
                        cooccurrence_matrix[aspect1][aspect2] += 1
                        
                        # Count unique pairs
                        pair = tuple(sorted([aspect1, aspect2]))
                        aspect_pairs[pair] += 1
        
        # Convert to regular dict for JSON serialization
        cooccurrence_dict = {
            aspect1: dict(aspect2_counts) 
            for aspect1, aspect2_counts in cooccurrence_matrix.items()
        }
        
        # Top co-occurring pairs
        top_pairs = sorted(aspect_pairs.items(), key=lambda x: x[1], reverse=True)[:20]
        
        analysis = {
            'cooccurrence_matrix': cooccurrence_dict,
            'top_cooccurring_pairs': [
                {'aspects': list(pair), 'count': count} 
                for pair, count in top_pairs
            ]
        }
        
        # Save analysis
        filepath = PROCESSED_DATA_DIR / 'aspect_cooccurrence.json'
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(analysis, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Co-occurrence analysis saved to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving co-occurrence analysis: {str(e)}")
        
        return analysis
    
    def save_extracted_aspects(self, reviews_data: List[Dict], filename: str = 'reviews_with_aspects.json'):
        """Save reviews with extracted aspects"""
        filepath = PROCESSED_DATA_DIR / filename
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(reviews_data, f, indent=2, ensure_ascii=False)
            
            # Also save as CSV for easy viewing
            df = pd.DataFrame(reviews_data)
            csv_filepath = filepath.with_suffix('.csv')
            df.to_csv(csv_filepath, index=False, encoding='utf-8')
            
            logger.info(f"Reviews with aspects saved to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving reviews with aspects: {str(e)}")
    
    def get_aspect_summary(self, reviews_data: List[Dict]) -> Dict:
        """Get summary of aspect extraction results"""
        total_reviews = len(reviews_data)
        reviews_with_aspects = sum(1 for r in reviews_data if r.get('aspect_count', 0) > 0)
        
        all_aspects = []
        for review in reviews_data:
            all_aspects.extend(review.get('mentioned_aspects', []))
        
        aspect_counts = Counter(all_aspects)
        
        summary = {
            'total_reviews': total_reviews,
            'reviews_with_aspects': reviews_with_aspects,
            'aspect_coverage': reviews_with_aspects / total_reviews if total_reviews > 0 else 0,
            'total_aspect_mentions': len(all_aspects),
            'unique_aspects_found': len(aspect_counts),
            'avg_aspects_per_review': len(all_aspects) / total_reviews if total_reviews > 0 else 0,
            'most_common_aspects': dict(aspect_counts.most_common(10))
        }
        
        return summary

if __name__ == "__main__":
    # Configure logging for standalone execution
    logging.basicConfig(level=logging.INFO)
    
    extractor = AspectExtractor()
    
    # Example usage
    sample_text = "The acting was excellent and the plot was engaging. The cinematography was beautiful but the dialogue felt forced."
    aspects = extractor.extract_aspects_from_text(sample_text)
    print(f"Extracted aspects: {aspects}")
    
    # Example with dummy reviews
    dummy_reviews = [
        {'text': 'Great acting and amazing plot!', 'rating': 9},
        {'text': 'Poor direction but good music.', 'rating': 6}
    ]
    
    enriched = extractor.extract_aspects_from_reviews(dummy_reviews)
    summary = extractor.get_aspect_summary(enriched)
    print(f"Aspect extraction summary: {summary}")
