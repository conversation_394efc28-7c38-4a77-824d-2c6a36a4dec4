#!/usr/bin/env python3
"""
Production IMDB Scraper
Collects recent movies and reviews from IMDB using working endpoints
"""

import sys
import time
import requests
import pandas as pd
import json
from pathlib import Path
from datetime import datetime
import cloudscraper
from bs4 import BeautifulSoup
import re
from tqdm import tqdm
import random

# Add project root to path
sys.path.append(str(Path(__file__).parent))

try:
    from config import RAW_DATA_DIR, TARGET_YEAR
except ImportError:
    RAW_DATA_DIR = Path("data/raw")
    TARGET_YEAR = 2025

class ProductionIMDBScraper:
    """Production-ready IMDB scraper for recent movies and reviews"""
    
    def __init__(self):
        self.base_url = "https://www.imdb.com"
        self.mobile_url = "https://m.imdb.com"
        
        # Initialize CloudScraper with mobile browser profile
        self.scraper = cloudscraper.create_scraper(
            browser={'browser': 'chrome', 'platform': 'android', 'mobile': True}
        )
        
        # Ensure data directory exists
        RAW_DATA_DIR.mkdir(parents=True, exist_ok=True)
        
        print("🎭 Production IMDB Scraper Initialized")
        print(f"Target Year: {TARGET_YEAR}")
        print(f"Data Directory: {RAW_DATA_DIR}")
    
    def search_recent_movies(self, year=None, max_movies=50):
        """Search for recent movies on IMDB"""
        if year is None:
            year = TARGET_YEAR
            
        print(f"\n🔍 Searching for {year} movies...")
        print("=" * 50)
        
        movies = []
        
        # Try different search approaches
        search_urls = [
            f"{self.base_url}/search/title/?release_date={year}&title_type=feature&sort=popularity,desc",
            f"{self.base_url}/search/title/?year={year}&title_type=feature&sort=num_votes,desc",
            f"{self.mobile_url}/search/title/?release_date={year}&title_type=feature"
        ]
        
        for i, search_url in enumerate(search_urls):
            print(f"\n🔄 Trying search method {i+1}...")
            
            try:
                response = self.scraper.get(search_url, timeout=15)
                
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    found_movies = self.extract_movie_list(soup)
                    
                    if found_movies:
                        movies.extend(found_movies)
                        print(f"✅ Found {len(found_movies)} movies")
                        
                        if len(movies) >= max_movies:
                            break
                    else:
                        print("⚠️ No movies found with this method")
                else:
                    print(f"⚠️ Search failed with status {response.status_code}")
                
                time.sleep(random.uniform(2, 4))
                
            except Exception as e:
                print(f"❌ Search error: {str(e)}")
        
        # Remove duplicates
        unique_movies = []
        seen_ids = set()
        
        for movie in movies:
            movie_id = movie.get('imdb_id')
            if movie_id and movie_id not in seen_ids:
                unique_movies.append(movie)
                seen_ids.add(movie_id)
        
        print(f"\n✅ Found {len(unique_movies)} unique movies for {year}")
        return unique_movies[:max_movies]
    
    def extract_movie_list(self, soup):
        """Extract movie list from search results"""
        movies = []
        
        # Try different selectors for movie containers
        selectors = [
            '.lister-item',
            '.titleColumn',
            '.cli-item',
            '.ipc-metadata-list-summary-item'
        ]
        
        for selector in selectors:
            containers = soup.select(selector)
            if containers:
                print(f"   Using selector: {selector}")
                break
        
        if not containers:
            # Fallback: look for any links with /title/tt pattern
            containers = soup.find_all('a', href=re.compile(r'/title/tt\d+/'))
        
        for container in containers[:50]:  # Limit to avoid too many requests
            try:
                movie_data = self.extract_movie_from_container(container)
                if movie_data:
                    movies.append(movie_data)
            except Exception as e:
                continue
        
        return movies
    
    def extract_movie_from_container(self, container):
        """Extract movie data from a container element"""
        try:
            movie_data = {}
            
            # Find movie link
            link = container.find('a', href=re.compile(r'/title/tt\d+/'))
            if not link:
                link = container if container.name == 'a' else None
            
            if link:
                href = link.get('href', '')
                imdb_match = re.search(r'tt\d+', href)
                if imdb_match:
                    movie_data['imdb_id'] = imdb_match.group()
                    movie_data['title'] = link.get_text().strip()
            
            # Try to find year
            year_text = container.get_text()
            year_match = re.search(r'\b(20\d{2})\b', year_text)
            if year_match:
                movie_data['year'] = int(year_match.group(1))
            
            # Try to find rating
            rating_elem = container.find(class_=re.compile(r'rating'))
            if rating_elem:
                rating_text = rating_elem.get_text()
                rating_match = re.search(r'(\d+\.?\d*)', rating_text)
                if rating_match:
                    movie_data['rating'] = float(rating_match.group(1))
            
            return movie_data if len(movie_data) >= 2 else None
            
        except Exception:
            return None
    
    def scrape_movie_details(self, movie_id):
        """Scrape detailed movie information"""
        try:
            url = f"{self.mobile_url}/title/{movie_id}/"
            response = self.scraper.get(url, timeout=15)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                return self.extract_detailed_movie_info(soup, movie_id)
            else:
                print(f"   ⚠️ Failed to get details for {movie_id}")
                return None
                
        except Exception as e:
            print(f"   ❌ Error getting details for {movie_id}: {str(e)}")
            return None
    
    def extract_detailed_movie_info(self, soup, movie_id):
        """Extract detailed movie information from movie page"""
        try:
            movie_data = {'imdb_id': movie_id}
            
            # Title
            title_elem = soup.find('h1') or soup.find('title')
            if title_elem:
                title = title_elem.get_text().strip()
                title = re.sub(r'\s*\(\d{4}\).*', '', title)
                title = re.sub(r'\s*-\s*IMDb.*', '', title)
                movie_data['title'] = title
            
            # Rating
            page_text = soup.get_text()
            rating_match = re.search(r'(\d+\.?\d*)/10', page_text)
            if rating_match:
                movie_data['rating'] = float(rating_match.group(1))
            
            # Year
            year_match = re.search(r'\b(19|20)\d{2}\b', page_text)
            if year_match:
                movie_data['year'] = int(year_match.group())
            
            # Genres
            genre_links = soup.find_all('a', href=re.compile(r'/genre/'))
            if genre_links:
                genres = [link.get_text().strip() for link in genre_links[:5]]
                movie_data['genres'] = genres
            
            # Plot
            plot_patterns = [
                r'Plot[:\s]*([^\.]+\.)',
                r'Summary[:\s]*([^\.]+\.)',
                r'storyline[:\s]*([^\.]+\.)'
            ]
            
            for pattern in plot_patterns:
                plot_match = re.search(pattern, page_text, re.IGNORECASE)
                if plot_match:
                    movie_data['plot'] = plot_match.group(1).strip()
                    break
            
            # Director
            director_links = soup.find_all('a', href=re.compile(r'/name/nm\d+/'))
            if director_links:
                # Usually the first name link is the director
                movie_data['director'] = director_links[0].get_text().strip()
            
            return movie_data
            
        except Exception as e:
            print(f"   ⚠️ Error extracting details: {str(e)}")
            return {'imdb_id': movie_id}
    
    def scrape_movie_reviews(self, movie_id, movie_title, max_reviews=100):
        """Scrape reviews for a specific movie"""
        print(f"   📝 Collecting reviews for {movie_title}...")
        
        reviews = []
        
        try:
            # Try mobile reviews page
            reviews_url = f"{self.mobile_url}/title/{movie_id}/reviews"
            response = self.scraper.get(reviews_url, timeout=15)
            
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                reviews = self.extract_reviews_from_page(soup, movie_id, movie_title)
                
                if reviews:
                    print(f"   ✅ Found {len(reviews)} reviews")
                else:
                    print(f"   ⚠️ No reviews found")
            else:
                print(f"   ⚠️ Reviews page returned {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Error getting reviews: {str(e)}")
        
        return reviews[:max_reviews]
    
    def extract_reviews_from_page(self, soup, movie_id, movie_title):
        """Extract reviews from reviews page"""
        reviews = []
        
        # Look for review containers
        review_containers = (
            soup.find_all('div', class_='review-container') or
            soup.find_all('div', class_='lister-item') or
            soup.find_all('div', class_='review-item')
        )
        
        for container in review_containers:
            try:
                review_data = {
                    'movie_id': movie_id,
                    'movie_title': movie_title,
                    'source': 'imdb'
                }
                
                # Author
                author_elem = container.find('span', class_='display-name-link')
                if author_elem:
                    review_data['author'] = author_elem.get_text().strip()
                
                # Rating
                rating_elem = container.find('span', class_='rating-other-user-rating')
                if rating_elem:
                    rating_text = rating_elem.get_text()
                    rating_match = re.search(r'(\d+)', rating_text)
                    if rating_match:
                        review_data['rating'] = int(rating_match.group(1))
                
                # Review text
                text_elem = container.find('div', class_='text')
                if text_elem:
                    review_text = text_elem.get_text().strip()
                    review_data['text'] = review_text
                    review_data['text_length'] = len(review_text)
                    review_data['word_count'] = len(review_text.split())
                
                # Date
                date_elem = container.find('span', class_='review-date')
                if date_elem:
                    review_data['date'] = date_elem.get_text().strip()
                
                if 'text' in review_data and len(review_data['text']) > 20:
                    reviews.append(review_data)
                    
            except Exception:
                continue
        
        return reviews
    
    def run_full_scraping(self, max_movies=10, max_reviews_per_movie=50):
        """Run complete IMDB scraping process"""
        print("🚀 Starting Production IMDB Scraping")
        print("=" * 60)
        print(f"Target: {max_movies} movies, {max_reviews_per_movie} reviews each")
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        start_time = time.time()
        
        # Step 1: Search for recent movies
        movies = self.search_recent_movies(TARGET_YEAR, max_movies)
        
        if not movies:
            print("❌ No movies found. Exiting.")
            return
        
        # Step 2: Get detailed info for each movie
        print(f"\n🔍 Getting detailed info for {len(movies)} movies...")
        detailed_movies = []
        all_reviews = []
        
        for i, movie in enumerate(movies, 1):
            movie_id = movie.get('imdb_id')
            movie_title = movie.get('title', 'Unknown')
            
            print(f"\n[{i}/{len(movies)}] Processing: {movie_title} ({movie_id})")
            
            # Get detailed movie info
            detailed_info = self.scrape_movie_details(movie_id)
            if detailed_info:
                # Merge with basic info
                combined_info = {**movie, **detailed_info}
                detailed_movies.append(combined_info)
                
                # Get reviews
                reviews = self.scrape_movie_reviews(movie_id, movie_title, max_reviews_per_movie)
                all_reviews.extend(reviews)
            
            # Rate limiting
            time.sleep(random.uniform(2, 4))
        
        # Step 3: Save data
        self.save_scraped_data(detailed_movies, all_reviews)
        
        # Summary
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n🎉 IMDB Scraping Complete!")
        print("=" * 60)
        print(f"⏱️  Duration: {duration:.1f} seconds")
        print(f"🎬 Movies scraped: {len(detailed_movies)}")
        print(f"📝 Reviews collected: {len(all_reviews)}")
        print(f"📊 Avg reviews per movie: {len(all_reviews)/len(detailed_movies):.1f}")
        
        return detailed_movies, all_reviews
    
    def save_scraped_data(self, movies, reviews):
        """Save scraped data to CSV files"""
        print(f"\n💾 Saving scraped data...")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save movies
        if movies:
            movies_df = pd.DataFrame(movies)
            movies_file = RAW_DATA_DIR / f"imdb_scraped_movies_{timestamp}.csv"
            movies_df.to_csv(movies_file, index=False, encoding='utf-8')
            print(f"✅ Movies saved: {movies_file}")
        
        # Save reviews
        if reviews:
            reviews_df = pd.DataFrame(reviews)
            reviews_file = RAW_DATA_DIR / f"imdb_scraped_reviews_{timestamp}.csv"
            reviews_df.to_csv(reviews_file, index=False, encoding='utf-8')
            print(f"✅ Reviews saved: {reviews_file}")
        
        # Save summary
        summary = {
            'scraping_timestamp': datetime.now().isoformat(),
            'target_year': TARGET_YEAR,
            'movies_scraped': len(movies),
            'reviews_collected': len(reviews),
            'data_source': 'IMDB Mobile Site',
            'scraping_method': 'CloudScraper + BeautifulSoup'
        }
        
        summary_file = RAW_DATA_DIR / f"imdb_scraping_summary_{timestamp}.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Summary saved: {summary_file}")

def main():
    """Main execution function"""
    scraper = ProductionIMDBScraper()
    
    # Run scraping: 5 movies, 50 reviews each
    scraper.run_full_scraping(max_movies=5, max_reviews_per_movie=50)

if __name__ == "__main__":
    main()
