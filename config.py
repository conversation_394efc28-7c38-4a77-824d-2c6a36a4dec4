"""
Global Configuration File for IMDB Aspect-Based Sentiment Analysis
"""

import os
from pathlib import Path

# Project Paths
PROJECT_ROOT = Path(__file__).parent
DATA_DIR = PROJECT_ROOT / "data"
RAW_DATA_DIR = DATA_DIR / "raw"
PROCESSED_DATA_DIR = DATA_DIR / "processed"
RESULTS_DIR = DATA_DIR / "results"
MODELS_DIR = PROJECT_ROOT / "models"

# Create directories if they don't exist
for directory in [DATA_DIR, RAW_DATA_DIR, PROCESSED_DATA_DIR, RESULTS_DIR, MODELS_DIR]:
    directory.mkdir(exist_ok=True)

# API Configuration
TMDB_API_KEY = os.getenv('TMDB_API_KEY', 'your_tmdb_api_key_here')
TMDB_BASE_URL = "https://api.themoviedb.org/3"

# Scraping Configuration
IMDB_BASE_URL = "https://www.imdb.com"
USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
REQUEST_DELAY = 1  # Delay between requests in seconds
MAX_RETRIES = 3

# Data Collection Settings
TARGET_YEAR = 2025
MAX_MOVIES_PER_GENRE = 50
MAX_REVIEWS_PER_MOVIE = 100
MIN_REVIEW_LENGTH = 50  # Minimum characters in a review

# Text Processing Configuration
LANGUAGES = ['en']  # Focus on English reviews
STOP_WORDS_REMOVE = True
LEMMATIZATION = True
MIN_WORD_LENGTH = 2
MAX_FEATURES = 10000  # For TF-IDF vectorization

# Sentiment Analysis Configuration
SENTIMENT_MODELS = {
    'vader': True,
    'textblob': True,
    'bert': True,
    'custom_ml': True
}

# Movie Aspects for Analysis
MOVIE_ASPECTS = [
    'plot',
    'acting',
    'direction',
    'cinematography',
    'music',
    'dialogue',
    'characters',
    'pacing',
    'effects',
    'overall'
]

# Aspect Keywords Dictionary
ASPECT_KEYWORDS = {
    'plot': ['story', 'plot', 'storyline', 'narrative', 'script', 'screenplay'],
    'acting': ['acting', 'performance', 'actor', 'actress', 'cast', 'character portrayal'],
    'direction': ['direction', 'director', 'directing', 'filmmaker', 'vision'],
    'cinematography': ['cinematography', 'camera', 'visual', 'shot', 'lighting', 'photography'],
    'music': ['music', 'soundtrack', 'score', 'sound', 'audio', 'composer'],
    'dialogue': ['dialogue', 'conversation', 'lines', 'writing', 'script'],
    'characters': ['character', 'protagonist', 'antagonist', 'development', 'personality'],
    'pacing': ['pacing', 'pace', 'rhythm', 'flow', 'timing', 'speed'],
    'effects': ['effects', 'cgi', 'special effects', 'visual effects', 'vfx'],
    'overall': ['movie', 'film', 'overall', 'general', 'experience']
}

# Model Configuration
BERT_MODEL_NAME = 'bert-base-uncased'
MAX_SEQUENCE_LENGTH = 512
BATCH_SIZE = 16
LEARNING_RATE = 2e-5
EPOCHS = 3

# Visualization Configuration
PLOT_STYLE = 'seaborn-v0_8'
FIGURE_SIZE = (12, 8)
COLOR_PALETTE = 'viridis'

# Dashboard Configuration
STREAMLIT_PORT = 8501
DASHBOARD_TITLE = "IMDB Aspect-Based Sentiment Analysis Dashboard"

# Database Configuration (if using)
DATABASE_URL = os.getenv('DATABASE_URL', 'sqlite:///imdb_sentiment.db')

# Logging Configuration
LOG_LEVEL = 'INFO'
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
LOG_FILE = PROJECT_ROOT / 'logs' / 'imdb_analysis.log'

# Create logs directory
(PROJECT_ROOT / 'logs').mkdir(exist_ok=True)
