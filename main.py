"""
Main Execution Script for IMDB Aspect-Based Sentiment Analysis
"""

import logging
import argparse
from pathlib import Path
import sys

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from config import LOG_LEVEL, LOG_FORMAT, LOG_FILE
from 01_data_collection.01_imdb_scraper import IMDBScraper
from 01_data_collection.02_tmdb_scraper import TMDBScraper
from 01_data_collection.03_review_scraper import ReviewScraper
from 02_data_cleansing.01_text_preprocessing import TextPreprocessor
from 03_eda_analysis.01_descriptive_stats import DescriptiveAnalysis
from 04_sentiment_analysis.03_aspect_sentiment import AspectSentimentAnalyzer
from 05_visualization.03_interactive_dashboard import launch_dashboard

# Configure logging
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format=LOG_FORMAT,
    handlers=[
        logging.FileHandler(LOG_FILE),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class IMDBAnalysisPipeline:
    """Main pipeline for IMDB aspect-based sentiment analysis"""
    
    def __init__(self):
        self.imdb_scraper = IMDBScraper()
        self.tmdb_scraper = TMDBScraper()
        self.review_scraper = ReviewScraper()
        self.text_processor = TextPreprocessor()
        self.descriptive_analyzer = DescriptiveAnalysis()
        self.sentiment_analyzer = AspectSentimentAnalyzer()
    
    def run_data_collection(self):
        """Step 1: Collect movie data and reviews"""
        logger.info("Starting data collection phase...")
        
        # Get 2025 movies from TMDB
        logger.info("Fetching 2025 movies from TMDB...")
        movies_data = self.tmdb_scraper.get_2025_movies()
        
        # Get additional IMDB data
        logger.info("Enriching data with IMDB information...")
        enriched_data = self.imdb_scraper.enrich_movie_data(movies_data)
        
        # Collect reviews for each movie
        logger.info("Collecting movie reviews...")
        reviews_data = self.review_scraper.collect_reviews(enriched_data)
        
        logger.info(f"Data collection completed. Collected {len(enriched_data)} movies and {len(reviews_data)} reviews.")
        return enriched_data, reviews_data
    
    def run_data_cleansing(self, movies_data, reviews_data):
        """Step 2: Clean and preprocess data"""
        logger.info("Starting data cleansing phase...")
        
        # Preprocess text data
        logger.info("Preprocessing review texts...")
        cleaned_reviews = self.text_processor.preprocess_reviews(reviews_data)
        
        # Clean movie metadata
        logger.info("Cleaning movie metadata...")
        cleaned_movies = self.text_processor.clean_movie_data(movies_data)
        
        logger.info("Data cleansing completed.")
        return cleaned_movies, cleaned_reviews
    
    def run_eda_analysis(self, movies_data, reviews_data):
        """Step 3: Exploratory Data Analysis"""
        logger.info("Starting EDA phase...")
        
        # Generate descriptive statistics
        logger.info("Generating descriptive statistics...")
        stats_report = self.descriptive_analyzer.generate_statistics(movies_data, reviews_data)
        
        # Create visualizations
        logger.info("Creating data visualizations...")
        self.descriptive_analyzer.create_visualizations(movies_data, reviews_data)
        
        logger.info("EDA completed.")
        return stats_report
    
    def run_sentiment_analysis(self, movies_data, reviews_data):
        """Step 4: Aspect-based sentiment analysis"""
        logger.info("Starting sentiment analysis phase...")
        
        # Perform aspect-based sentiment analysis
        logger.info("Analyzing sentiment by aspects...")
        sentiment_results = self.sentiment_analyzer.analyze_aspects(reviews_data)
        
        # Generate sentiment reports
        logger.info("Generating sentiment reports...")
        reports = self.sentiment_analyzer.generate_reports(sentiment_results, movies_data)
        
        logger.info("Sentiment analysis completed.")
        return sentiment_results, reports
    
    def run_full_pipeline(self):
        """Run the complete analysis pipeline"""
        logger.info("Starting full IMDB aspect-based sentiment analysis pipeline...")
        
        try:
            # Step 1: Data Collection
            movies_data, reviews_data = self.run_data_collection()
            
            # Step 2: Data Cleansing
            clean_movies, clean_reviews = self.run_data_cleansing(movies_data, reviews_data)
            
            # Step 3: EDA
            stats_report = self.run_eda_analysis(clean_movies, clean_reviews)
            
            # Step 4: Sentiment Analysis
            sentiment_results, reports = self.run_sentiment_analysis(clean_movies, clean_reviews)
            
            logger.info("Pipeline completed successfully!")
            return {
                'movies': clean_movies,
                'reviews': clean_reviews,
                'statistics': stats_report,
                'sentiment_results': sentiment_results,
                'reports': reports
            }
            
        except Exception as e:
            logger.error(f"Pipeline failed with error: {str(e)}")
            raise

def main():
    """Main function with command line interface"""
    parser = argparse.ArgumentParser(description='IMDB Aspect-Based Sentiment Analysis')
    parser.add_argument('--step', choices=['collect', 'clean', 'eda', 'sentiment', 'dashboard', 'full'], 
                       default='full', help='Which step to run')
    parser.add_argument('--dashboard-only', action='store_true', 
                       help='Launch dashboard only (requires existing data)')
    
    args = parser.parse_args()
    
    pipeline = IMDBAnalysisPipeline()
    
    if args.dashboard_only:
        logger.info("Launching dashboard...")
        launch_dashboard()
    elif args.step == 'collect':
        pipeline.run_data_collection()
    elif args.step == 'clean':
        # Load existing data and run cleaning
        logger.info("Running data cleansing on existing data...")
        # Implementation would load existing raw data
    elif args.step == 'eda':
        # Load existing data and run EDA
        logger.info("Running EDA on existing data...")
        # Implementation would load existing processed data
    elif args.step == 'sentiment':
        # Load existing data and run sentiment analysis
        logger.info("Running sentiment analysis on existing data...")
        # Implementation would load existing processed data
    elif args.step == 'dashboard':
        launch_dashboard()
    else:  # full pipeline
        results = pipeline.run_full_pipeline()
        logger.info("Full pipeline completed. Launching dashboard...")
        launch_dashboard()

if __name__ == "__main__":
    main()
