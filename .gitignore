# IMDB Aspect-Based Sentiment Analysis - .gitignore

# Environment Variables
.env
.env.local
.env.development
.env.test
.env.production

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints
*/.ipynb_checkpoints/*

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Data directories
data/raw/*.json
data/raw/*.csv
data/raw/*.txt
data/processed/*.json
data/processed/*.csv
data/processed/*.txt
data/results/*.json
data/results/*.csv
data/results/*.txt
data/results/visualizations/*.png
data/results/visualizations/*.jpg
data/results/visualizations/*.html

# Keep directory structure but ignore contents
data/raw/.gitkeep
data/processed/.gitkeep
data/results/.gitkeep
data/results/visualizations/.gitkeep

# Logs
logs/*.log
logs/*.txt
*.log

# Models (if any large model files are saved)
models/*.pkl
models/*.joblib
models/*.h5
models/*.pt
models/*.pth

# API Keys and Secrets
api_keys.txt
secrets.json
credentials.json

# IDE and Editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Backup files
*.bak
*.backup

# Database files
*.db
*.sqlite
*.sqlite3

# Cache directories
.cache/
cache/

# Streamlit
.streamlit/

# Large data files (adjust size as needed)
*.zip
*.tar.gz
*.rar

# Scraped data (can be large)
scraped_data/
raw_scrapes/

# Generated reports
reports/*.pdf
reports/*.docx
reports/*.html

# Notebook outputs (optional - uncomment if you don't want to track outputs)
# *.ipynb

# Virtual environment directories
.conda/
conda-meta/

# PyCharm
.idea/

# VS Code
.vscode/settings.json
.vscode/launch.json
.vscode/extensions.json

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Local configuration files
local_config.py
local_settings.py

# Test outputs
test_outputs/
test_results/

# Documentation builds
docs/_build/
docs/build/

# Profiling data
*.prof

# Coverage reports
.coverage
htmlcov/

# Pytest
.pytest_cache/

# Tox
.tox/

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# DotEnv configuration
.env*

# Database
*.db
*.rdb

# Pycharm
.idea

# VS Code
.vscode/

# Spyder
.spyproject/

# Jupyter NB Checkpoints
.ipynb_checkpoints/

# exclude data from source control by default
/data/

# Mac OS-specific
.DS_Store

# Project-specific ignores
# Uncomment and modify as needed for your specific project

# Large model files
# *.model
# *.bin

# Processed datasets (if very large)
# processed_*.json
# processed_*.csv

# API response caches
# api_cache/
# response_cache/

# Temporary analysis files
# analysis_temp/
# temp_analysis/
