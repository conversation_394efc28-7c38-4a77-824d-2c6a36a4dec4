# Usage Guide - IMDB Aspect-Based Sentiment Analysis

This guide provides detailed instructions on how to use the IMDB Aspect-Based Sentiment Analysis project.

## Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd python-absa-imdb

# Install dependencies
pip install -r requirements.txt

# Download required NLTK data
python -c "import nltk; nltk.download('punkt'); nltk.download('stopwords'); nltk.download('wordnet'); nltk.download('averaged_perceptron_tagger')"

# Download spaCy model
python -m spacy download en_core_web_sm
```

### 2. Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your API keys
# TMDB_API_KEY=your_actual_api_key_here
```

### 3. Run Complete Pipeline

```bash
# Run the full analysis pipeline
python main.py

# Or run specific steps
python main.py --step collect    # Data collection only
python main.py --step clean      # Data cleaning only
python main.py --step eda        # EDA only
python main.py --step sentiment  # Sentiment analysis only
python main.py --step dashboard  # Launch dashboard only
```

### 4. View Results

```bash
# Launch interactive dashboard
python main.py --dashboard-only

# Or manually launch
streamlit run 05_visualization/03_interactive_dashboard.py
```

## Detailed Usage

### Data Collection

#### TMDB Data Collection
```python
from 01_data_collection.02_tmdb_scraper import TMDBScraper

scraper = TMDBScraper(api_key="your_api_key")
movies_data = scraper.get_2025_movies()
```

#### IMDB Data Collection
```python
from 01_data_collection.01_imdb_scraper import IMDBScraper

scraper = IMDBScraper()
movies_data = scraper.run_full_scraping()
```

#### Review Collection
```python
from 01_data_collection.03_review_scraper import ReviewScraper

scraper = ReviewScraper()
reviews_data = scraper.collect_reviews(movies_data)
```

### Data Cleaning

#### Text Preprocessing
```python
from 02_data_cleansing.01_text_preprocessing import TextPreprocessor

processor = TextPreprocessor()

# Clean individual text
clean_text = processor.preprocess_text("This is a great movie!")

# Process all reviews
processed_reviews = processor.preprocess_reviews(reviews_data)
```

#### Data Validation
```python
from 02_data_cleansing.02_data_validation import DataQualityValidator

validator = DataQualityValidator()
report = validator.generate_validation_report(movies_data, reviews_data)
```

#### Duplicate Removal
```python
from 02_data_cleansing.03_duplicate_removal import DuplicateRemover

remover = DuplicateRemover()
unique_movies, unique_reviews, report = remover.remove_all_duplicates(movies_data, reviews_data)
```

### Exploratory Data Analysis

#### Descriptive Statistics
```python
from 03_eda_analysis.01_descriptive_stats import DescriptiveAnalysis

analyzer = DescriptiveAnalysis()
stats_report = analyzer.generate_statistics(movies_data, reviews_data)
```

#### Data Visualization
```python
from 03_eda_analysis.02_data_visualization import DataVisualizer

visualizer = DataVisualizer()
visualizer.create_visualizations(movies_data, reviews_data)
```

### Sentiment Analysis

#### Aspect Extraction
```python
from 04_sentiment_analysis.01_aspect_extraction import AspectExtractor

extractor = AspectExtractor()

# Extract aspects from single text
aspects = extractor.extract_aspects_from_text("The acting was great but the plot was confusing.")

# Extract from all reviews
enriched_reviews = extractor.extract_aspects_from_reviews(reviews_data)
```

#### Aspect-Based Sentiment Analysis
```python
from 04_sentiment_analysis.03_aspect_sentiment import AspectSentimentAnalyzer

analyzer = AspectSentimentAnalyzer()

# Analyze single review
sentiment_result = analyzer.analyze_aspect_sentiment(review)

# Analyze all reviews
sentiment_results = analyzer.analyze_aspects(reviews_data)

# Generate reports
reports = analyzer.generate_reports(sentiment_results, movies_data)
```

### Visualization and Dashboard

#### Interactive Dashboard
```python
from 05_visualization.03_interactive_dashboard import launch_dashboard

# Launch Streamlit dashboard
launch_dashboard()
```

## Configuration Options

### Data Collection Settings
```python
# In config.py
TARGET_YEAR = 2025
MAX_MOVIES_PER_GENRE = 50
MAX_REVIEWS_PER_MOVIE = 100
MIN_REVIEW_LENGTH = 50
```

### Text Processing Settings
```python
# In config.py
STOP_WORDS_REMOVE = True
LEMMATIZATION = True
MIN_WORD_LENGTH = 2
```

### Aspect Configuration
```python
# In config.py
MOVIE_ASPECTS = [
    'plot', 'acting', 'direction', 'cinematography',
    'music', 'dialogue', 'characters', 'pacing', 'effects'
]

ASPECT_KEYWORDS = {
    'plot': ['story', 'plot', 'storyline', 'narrative'],
    'acting': ['acting', 'performance', 'actor', 'actress'],
    # ... more aspects
}
```

## File Structure and Outputs

### Input Data
- `data/raw/tmdb_movies_2025.json` - Raw TMDB movie data
- `data/raw/imdb_movies_2025.json` - Raw IMDB movie data
- `data/raw/movie_reviews_2025.json` - Raw review data

### Processed Data
- `data/processed/cleaned_movies.json` - Cleaned movie data
- `data/processed/processed_reviews.json` - Processed review data
- `data/processed/reviews_with_aspects.json` - Reviews with extracted aspects

### Results
- `data/results/descriptive_statistics.json` - Statistical analysis results
- `data/results/aspect_sentiment_results.json` - Sentiment analysis results
- `data/results/sentiment_analysis_reports.json` - Comprehensive reports
- `data/results/visualizations/` - Generated plots and charts

## Advanced Usage

### Custom Aspect Definition
```python
# Define custom aspects
custom_aspects = ['soundtrack', 'editing', 'costumes']
custom_keywords = {
    'soundtrack': ['music', 'score', 'soundtrack', 'audio'],
    'editing': ['editing', 'cuts', 'transitions', 'pacing'],
    'costumes': ['costumes', 'wardrobe', 'clothing', 'outfits']
}

# Update configuration
from config import MOVIE_ASPECTS, ASPECT_KEYWORDS
MOVIE_ASPECTS.extend(custom_aspects)
ASPECT_KEYWORDS.update(custom_keywords)
```

### Custom Sentiment Models
```python
# Implement custom sentiment analyzer
class CustomSentimentAnalyzer:
    def analyze_sentiment(self, text):
        # Your custom implementation
        return {'score': 0.5, 'label': 'positive'}

# Use in pipeline
analyzer = AspectSentimentAnalyzer()
analyzer.custom_analyzer = CustomSentimentAnalyzer()
```

### Batch Processing
```python
# Process large datasets in batches
def process_in_batches(data, batch_size=100):
    for i in range(0, len(data), batch_size):
        batch = data[i:i+batch_size]
        # Process batch
        yield process_batch(batch)
```

## Troubleshooting

### Common Issues

1. **TMDB API Key Error**
   ```
   Error: TMDB API key not configured
   Solution: Set TMDB_API_KEY in .env file
   ```

2. **Memory Issues with Large Datasets**
   ```
   Solution: Process data in smaller batches
   ```

3. **Missing Dependencies**
   ```
   Solution: pip install -r requirements.txt
   ```

4. **NLTK Data Missing**
   ```
   Solution: Run NLTK downloads as shown in installation
   ```

### Performance Optimization

1. **Parallel Processing**
   ```python
   from multiprocessing import Pool
   
   def process_parallel(data, num_processes=4):
       with Pool(num_processes) as pool:
           results = pool.map(process_function, data)
       return results
   ```

2. **Caching Results**
   ```python
   import pickle
   
   # Save intermediate results
   with open('intermediate_results.pkl', 'wb') as f:
       pickle.dump(results, f)
   ```

## Testing

### Run Unit Tests
```bash
# Run all tests
python -m pytest tests/

# Run specific test
python -m pytest tests/test_modules.py::TestTextCleaner

# Run with coverage
python -m pytest tests/ --cov=.
```

### Manual Testing
```python
# Test individual components
from tests.test_modules import TestTextCleaner

test = TestTextCleaner()
test.setUp()
test.test_comprehensive_cleaning()
```

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the code documentation
3. Run the test suite to verify installation
4. Check the logs in `logs/imdb_analysis.log`
