#!/usr/bin/env python3
"""
Test Scraping Script
Tests the data collection functionality to ensure it's working correctly
"""

import sys
import json
import time
import requests
import os
from pathlib import Path
from datetime import datetime
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add project root to path
sys.path.append(str(Path(__file__).parent))

# Import basic config
try:
    from config import RAW_DATA_DIR, TARGET_YEAR
except ImportError:
    # Fallback if config import fails
    RAW_DATA_DIR = Path("data/raw")
    TARGET_YEAR = 2025

def test_tmdb_api():
    """Test TMDB API directly"""
    print("🎬 Testing TMDB API...")
    print("=" * 40)

    try:
        # Get API key from environment
        api_key = os.getenv('TMDB_API_KEY')

        if not api_key or api_key == 'your_tmdb_api_key_here':
            print("❌ TMDB API key not configured")
            print("Please set TMDB_API_KEY in your .env file")
            print("Get a free API key from: https://www.themoviedb.org/settings/api")
            return False

        print("✅ TMDB API key found")

        # Test API connection with a simple request
        print("📡 Testing API connection...")
        base_url = "https://api.themoviedb.org/3"
        test_url = f"{base_url}/movie/550?api_key={api_key}"  # Fight Club

        response = requests.get(test_url, timeout=10)

        if response.status_code == 200:
            movie_data = response.json()
            print("✅ API connection successful")
            print(f"Test movie: {movie_data.get('title', 'Unknown')}")
            print(f"Release date: {movie_data.get('release_date', 'Unknown')}")
            print(f"Rating: {movie_data.get('vote_average', 'Unknown')}")
        else:
            print(f"❌ API connection failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False

        # Test movie discovery for target year
        print(f"\n🔍 Testing movie discovery for {TARGET_YEAR}...")
        discover_url = f"{base_url}/discover/movie"
        params = {
            'api_key': api_key,
            'primary_release_year': TARGET_YEAR,
            'sort_by': 'popularity.desc',
            'page': 1
        }

        response = requests.get(discover_url, params=params, timeout=10)

        if response.status_code == 200:
            data = response.json()
            movies = data.get('results', [])
            print(f"✅ Found {len(movies)} movies for {TARGET_YEAR}")

            if movies:
                print("Sample movies:")
                for i, movie in enumerate(movies[:3]):
                    print(f"  {i+1}. {movie.get('title', 'Unknown')} ({movie.get('release_date', 'Unknown')})")
            else:
                print(f"⚠️ No movies found for {TARGET_YEAR}")
        else:
            print(f"❌ Movie discovery failed: {response.status_code}")
            return False

        return True

    except requests.exceptions.RequestException as e:
        print(f"❌ Network error: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ TMDB API error: {str(e)}")
        return False

def test_imdb_connection():
    """Test IMDB connection (basic connectivity test)"""
    print("\n🎭 Testing IMDB Connection...")
    print("=" * 40)

    try:
        # Test basic IMDB connectivity
        print("🔍 Testing IMDB website connectivity...")

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        # Test connection to IMDB
        response = requests.get('https://www.imdb.com/', headers=headers, timeout=10)

        if response.status_code == 200:
            print("✅ IMDB website accessible")
            print(f"Response size: {len(response.content)} bytes")
        else:
            print(f"⚠️ IMDB returned status code: {response.status_code}")

        # Test a specific movie page
        print("\n🔍 Testing specific movie page access...")
        movie_url = 'https://www.imdb.com/title/tt0137523/'  # Fight Club
        response = requests.get(movie_url, headers=headers, timeout=10)

        if response.status_code == 200:
            print("✅ Movie page accessible")
            if 'Fight Club' in response.text:
                print("✅ Page content looks correct")
            else:
                print("⚠️ Page content might be blocked or different")
        else:
            print(f"⚠️ Movie page returned status code: {response.status_code}")

        print("\nNote: IMDB has anti-scraping measures. Full scraping may require more sophisticated techniques.")
        return True

    except requests.exceptions.RequestException as e:
        print(f"❌ Network error: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ IMDB connection error: {str(e)}")
        return False

def test_review_sources():
    """Test review source accessibility"""
    print("\n📝 Testing Review Sources...")
    print("=" * 40)

    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        # Test IMDB reviews page
        print("🔍 Testing IMDB reviews accessibility...")
        imdb_reviews_url = 'https://www.imdb.com/title/tt0137523/reviews'  # Fight Club reviews

        response = requests.get(imdb_reviews_url, headers=headers, timeout=10)

        if response.status_code == 200:
            print("✅ IMDB reviews page accessible")
            if 'review' in response.text.lower():
                print("✅ Reviews content detected")
            else:
                print("⚠️ Reviews content might be blocked")
        else:
            print(f"⚠️ IMDB reviews returned status code: {response.status_code}")

        # Test TMDB API for reviews (if API key available)
        api_key = os.getenv('TMDB_API_KEY')
        if api_key and api_key != 'your_tmdb_api_key_here':
            print("\n🔍 Testing TMDB reviews via API...")
            tmdb_reviews_url = f"https://api.themoviedb.org/3/movie/550/reviews?api_key={api_key}"

            response = requests.get(tmdb_reviews_url, timeout=10)

            if response.status_code == 200:
                data = response.json()
                reviews = data.get('results', [])
                print(f"✅ TMDB API reviews accessible: {len(reviews)} reviews found")

                if reviews:
                    sample_review = reviews[0]
                    print(f"Sample review author: {sample_review.get('author', 'Unknown')}")
                    content = sample_review.get('content', '')
                    print(f"Sample content: {content[:100]}{'...' if len(content) > 100 else ''}")
            else:
                print(f"⚠️ TMDB reviews returned status code: {response.status_code}")
        else:
            print("\n⚠️ TMDB API key not available for review testing")

        print("\nNote: Review scraping requires careful handling of anti-bot measures")
        return True

    except requests.exceptions.RequestException as e:
        print(f"❌ Network error: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Review source error: {str(e)}")
        return False

def test_data_saving():
    """Test data saving functionality"""
    print("\n💾 Testing Data Saving...")
    print("=" * 40)
    
    try:
        # Create test data
        test_data = {
            'test_timestamp': datetime.now().isoformat(),
            'test_movies': [
                {'title': 'Test Movie 1', 'year': 2025},
                {'title': 'Test Movie 2', 'year': 2025}
            ],
            'test_reviews': [
                {'text': 'Great movie!', 'rating': 9},
                {'text': 'Not bad', 'rating': 6}
            ]
        }
        
        # Save test data
        test_file = RAW_DATA_DIR / 'scraping_test.json'
        with open(test_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Test data saved to {test_file}")
        
        # Verify file was created and can be read
        with open(test_file, 'r', encoding='utf-8') as f:
            loaded_data = json.load(f)
        
        print(f"✅ Test data successfully loaded back")
        print(f"   Movies: {len(loaded_data['test_movies'])}")
        print(f"   Reviews: {len(loaded_data['test_reviews'])}")
        
        # Clean up test file
        test_file.unlink()
        print("✅ Test file cleaned up")
        
        return True
        
    except Exception as e:
        print(f"❌ Data saving error: {str(e)}")
        return False

def main():
    """Run all scraping tests"""
    print("🧪 IMDB Aspect-Based Sentiment Analysis - Scraping Tests")
    print("=" * 60)
    print(f"Target year: {TARGET_YEAR}")
    print(f"Data directory: {RAW_DATA_DIR}")
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Ensure data directory exists
    RAW_DATA_DIR.mkdir(parents=True, exist_ok=True)
    
    # Run tests
    results = {}

    # Test TMDB API
    results['tmdb_api'] = test_tmdb_api()
    time.sleep(1)  # Rate limiting

    # Test IMDB connection (with caution)
    results['imdb_connection'] = test_imdb_connection()
    time.sleep(2)  # Rate limiting

    # Test review sources
    results['review_sources'] = test_review_sources()
    time.sleep(1)  # Rate limiting

    # Test data saving
    results['data_saving'] = test_data_saving()
    
    # Summary
    print("\n" + "=" * 60)
    print("🏁 Test Summary")
    print("=" * 60)
    
    for test_name, success in results.items():
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name.upper()}: {status}")
    
    total_passed = sum(results.values())
    total_tests = len(results)
    
    print(f"\nOverall: {total_passed}/{total_tests} tests passed")
    
    if total_passed == total_tests:
        print("🎉 All tests passed! Your scraping setup is working correctly.")
    elif total_passed > 0:
        print("⚠️ Some tests passed. Check the failed tests above.")
    else:
        print("❌ All tests failed. Please check your configuration and network connection.")
    
    print("\n📋 Next Steps:")
    if not results.get('tmdb'):
        print("- Configure TMDB API key in .env file")
    if results.get('tmdb'):
        print("- Run full data collection: python main.py --step collect")
    print("- Check the main.py script for complete pipeline execution")
    print("- Review the logs for detailed error information")

if __name__ == "__main__":
    main()
