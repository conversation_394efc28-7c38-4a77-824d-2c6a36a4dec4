"""
Movie Review Scraper
Collects movie reviews from multiple sources (IMDB, TMDB, etc.)
"""

import requests
from bs4 import BeautifulSoup
import pandas as pd
import json
import logging
from typing import List, Dict, Optional
import time
import re
from pathlib import Path
import sys

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from config import RAW_DATA_DIR, MAX_REVIEWS_PER_MOVIE, MIN_REVIEW_LENGTH
from 01_data_collection.config.scraping_config import IMDB_CONFIG, IMDB_SELECTORS

logger = logging.getLogger(__name__)

class ReviewScraper:
    """Scraper for movie reviews from various sources"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update(IMDB_CONFIG['headers'])
        self.reviews_data = []
    
    def scrape_imdb_reviews(self, imdb_id: str, max_reviews: int = MAX_REVIEWS_PER_MOVIE) -> List[Dict]:
        """Scrape reviews from IMDB for a specific movie"""
        logger.debug(f"Scraping IMDB reviews for {imdb_id}")
        
        reviews_url = IMDB_CONFIG['reviews_url_template'].format(imdb_id=imdb_id)
        reviews = []
        
        try:
            # Get first page of reviews
            response = self.session.get(reviews_url, timeout=IMDB_CONFIG['timeout'])
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Find review containers
            review_containers = soup.find_all('div', class_='review-container')
            
            for container in review_containers[:max_reviews]:
                review_data = self._extract_imdb_review(container, imdb_id)
                if review_data and len(review_data.get('text', '')) >= MIN_REVIEW_LENGTH:
                    reviews.append(review_data)
            
            logger.debug(f"Collected {len(reviews)} reviews for {imdb_id}")
            time.sleep(IMDB_CONFIG['request_delay'])
            
        except Exception as e:
            logger.error(f"Error scraping IMDB reviews for {imdb_id}: {str(e)}")
        
        return reviews
    
    def _extract_imdb_review(self, container, imdb_id: str) -> Optional[Dict]:
        """Extract review data from IMDB review container"""
        try:
            review_data = {
                'imdb_id': imdb_id,
                'source': 'imdb'
            }
            
            # Review title
            title_elem = container.find('a', class_='title')
            if title_elem:
                review_data['title'] = title_elem.text.strip()
            
            # Review text
            text_elem = container.find('div', class_='text')
            if text_elem:
                review_data['text'] = text_elem.text.strip()
            
            # Rating
            rating_elem = container.find('span', class_='rating-other-user-rating')
            if rating_elem:
                rating_span = rating_elem.find('span')
                if rating_span:
                    try:
                        review_data['rating'] = int(rating_span.text.strip())
                    except ValueError:
                        pass
            
            # Author
            author_elem = container.find('span', class_='display-name-link')
            if author_elem:
                review_data['author'] = author_elem.text.strip()
            
            # Date
            date_elem = container.find('span', class_='review-date')
            if date_elem:
                review_data['date'] = date_elem.text.strip()
            
            # Helpful votes
            helpful_elem = container.find('div', class_='actions')
            if helpful_elem:
                helpful_text = helpful_elem.text
                helpful_match = re.search(r'(\d+) out of (\d+) found this helpful', helpful_text)
                if helpful_match:
                    review_data['helpful_votes'] = int(helpful_match.group(1))
                    review_data['total_votes'] = int(helpful_match.group(2))
            
            return review_data
            
        except Exception as e:
            logger.warning(f"Error extracting review: {str(e)}")
            return None
    
    def process_tmdb_reviews(self, tmdb_reviews: List[Dict], tmdb_id: int) -> List[Dict]:
        """Process TMDB reviews data"""
        processed_reviews = []
        
        for review in tmdb_reviews:
            try:
                review_data = {
                    'tmdb_id': tmdb_id,
                    'source': 'tmdb',
                    'id': review.get('id'),
                    'author': review.get('author'),
                    'text': review.get('content', ''),
                    'created_at': review.get('created_at'),
                    'updated_at': review.get('updated_at'),
                    'url': review.get('url')
                }
                
                # Extract rating if available
                author_details = review.get('author_details', {})
                if author_details and 'rating' in author_details:
                    review_data['rating'] = author_details['rating']
                
                # Only include reviews with sufficient length
                if len(review_data['text']) >= MIN_REVIEW_LENGTH:
                    processed_reviews.append(review_data)
                    
            except Exception as e:
                logger.warning(f"Error processing TMDB review: {str(e)}")
        
        return processed_reviews
    
    def collect_reviews(self, movies_data: List[Dict]) -> List[Dict]:
        """Collect reviews for all movies"""
        logger.info(f"Collecting reviews for {len(movies_data)} movies")
        
        all_reviews = []
        
        for i, movie in enumerate(movies_data):
            movie_title = movie.get('title', 'Unknown')
            logger.info(f"Collecting reviews for movie {i+1}/{len(movies_data)}: {movie_title}")
            
            # Collect IMDB reviews
            imdb_id = movie.get('imdb_id')
            if imdb_id:
                imdb_reviews = self.scrape_imdb_reviews(imdb_id)
                all_reviews.extend(imdb_reviews)
            
            # Process TMDB reviews if available
            tmdb_reviews = movie.get('tmdb_reviews', [])
            tmdb_id = movie.get('id') or movie.get('tmdb_id')
            if tmdb_reviews and tmdb_id:
                processed_tmdb_reviews = self.process_tmdb_reviews(tmdb_reviews, tmdb_id)
                all_reviews.extend(processed_tmdb_reviews)
        
        logger.info(f"Collected {len(all_reviews)} total reviews")
        
        # Save reviews data
        self.save_reviews_data(all_reviews)
        
        return all_reviews
    
    def save_reviews_data(self, reviews_data: List[Dict], filename: str = 'movie_reviews_2025.json'):
        """Save reviews data to file"""
        filepath = RAW_DATA_DIR / filename
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(reviews_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Saved {len(reviews_data)} reviews to {filepath}")
            
            # Also save as CSV
            df = pd.DataFrame(reviews_data)
            csv_filepath = filepath.with_suffix('.csv')
            df.to_csv(csv_filepath, index=False, encoding='utf-8')
            logger.info(f"Also saved as CSV: {csv_filepath}")
            
            # Create summary statistics
            self._create_review_summary(reviews_data)
            
        except Exception as e:
            logger.error(f"Error saving reviews data: {str(e)}")
    
    def _create_review_summary(self, reviews_data: List[Dict]):
        """Create summary statistics for reviews"""
        try:
            summary = {
                'total_reviews': len(reviews_data),
                'sources': {},
                'avg_review_length': 0,
                'reviews_with_ratings': 0,
                'avg_rating': 0
            }
            
            total_length = 0
            total_ratings = 0
            rating_sum = 0
            
            for review in reviews_data:
                source = review.get('source', 'unknown')
                summary['sources'][source] = summary['sources'].get(source, 0) + 1
                
                text_length = len(review.get('text', ''))
                total_length += text_length
                
                if 'rating' in review and review['rating'] is not None:
                    total_ratings += 1
                    rating_sum += review['rating']
            
            if reviews_data:
                summary['avg_review_length'] = total_length / len(reviews_data)
            
            if total_ratings > 0:
                summary['reviews_with_ratings'] = total_ratings
                summary['avg_rating'] = rating_sum / total_ratings
            
            # Save summary
            summary_filepath = RAW_DATA_DIR / 'reviews_summary.json'
            with open(summary_filepath, 'w', encoding='utf-8') as f:
                json.dump(summary, f, indent=2)
            
            logger.info(f"Created review summary: {summary}")
            
        except Exception as e:
            logger.error(f"Error creating review summary: {str(e)}")
    
    def filter_reviews_by_quality(self, reviews_data: List[Dict]) -> List[Dict]:
        """Filter reviews based on quality criteria"""
        filtered_reviews = []
        
        for review in reviews_data:
            text = review.get('text', '')
            
            # Length filter
            if len(text) < MIN_REVIEW_LENGTH:
                continue
            
            # Language filter (basic English detection)
            if not self._is_likely_english(text):
                continue
            
            # Spam filter (basic)
            if self._is_likely_spam(text):
                continue
            
            filtered_reviews.append(review)
        
        logger.info(f"Filtered {len(reviews_data)} reviews to {len(filtered_reviews)} quality reviews")
        return filtered_reviews
    
    def _is_likely_english(self, text: str) -> bool:
        """Basic English language detection"""
        english_words = ['the', 'and', 'is', 'in', 'to', 'of', 'a', 'that', 'it', 'with']
        text_lower = text.lower()
        english_count = sum(1 for word in english_words if word in text_lower)
        return english_count >= 3
    
    def _is_likely_spam(self, text: str) -> bool:
        """Basic spam detection"""
        spam_indicators = ['click here', 'visit our website', 'buy now', 'free download']
        text_lower = text.lower()
        return any(indicator in text_lower for indicator in spam_indicators)

if __name__ == "__main__":
    # Configure logging for standalone execution
    logging.basicConfig(level=logging.INFO)
    
    # Example usage with dummy data
    scraper = ReviewScraper()
    
    # This would normally be called with actual movie data
    dummy_movies = [
        {'imdb_id': 'tt1234567', 'title': 'Test Movie', 'tmdb_reviews': []}
    ]
    
    reviews = scraper.collect_reviews(dummy_movies)
    print(f"Collected {len(reviews)} reviews")
