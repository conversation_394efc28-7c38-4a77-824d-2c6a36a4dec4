"""
Data Visualization Module
Creates comprehensive visualizations for movie and review data
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import logging
from typing import List, Dict, Optional
from pathlib import Path
import sys
from wordcloud import WordCloud
import json

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from config import RESULTS_DIR, PLOT_STYLE, FIGURE_SIZE, COLOR_PALETTE

logger = logging.getLogger(__name__)

class DataVisualizer:
    """Comprehensive data visualization class"""
    
    def __init__(self):
        # Set plotting styles
        plt.style.use(PLOT_STYLE)
        sns.set_palette(COLOR_PALETTE)
        self.figure_size = FIGURE_SIZE
        
        # Create visualization directory
        self.viz_dir = RESULTS_DIR / 'visualizations'
        self.viz_dir.mkdir(exist_ok=True)
    
    def create_movie_visualizations(self, movies_data: List[Dict]):
        """Create comprehensive movie visualizations"""
        logger.info("Creating movie visualizations")
        
        df = pd.DataFrame(movies_data)
        
        # Rating distribution
        self._plot_rating_distribution(df)
        
        # Genre analysis
        self._plot_genre_analysis(df)
        
        # Year distribution
        self._plot_year_distribution(df)
        
        # Budget vs Revenue
        self._plot_budget_revenue(df)
        
        # Runtime analysis
        self._plot_runtime_analysis(df)
        
        # Interactive dashboard
        self._create_movie_dashboard(df)
    
    def _plot_rating_distribution(self, df: pd.DataFrame):
        """Plot movie rating distribution"""
        rating_cols = ['rating', 'vote_average', 'imdb_rating']
        rating_col = None
        
        for col in rating_cols:
            if col in df.columns and df[col].notna().sum() > 0:
                rating_col = col
                break
        
        if not rating_col:
            logger.warning("No rating data available for visualization")
            return
        
        ratings = df[rating_col].dropna()
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Movie Rating Analysis', fontsize=16, fontweight='bold')
        
        # Histogram
        axes[0, 0].hist(ratings, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 0].set_title('Rating Distribution')
        axes[0, 0].set_xlabel('Rating')
        axes[0, 0].set_ylabel('Frequency')
        
        # Box plot
        axes[0, 1].boxplot(ratings)
        axes[0, 1].set_title('Rating Box Plot')
        axes[0, 1].set_ylabel('Rating')
        
        # Density plot
        ratings.plot.density(ax=axes[1, 0], color='orange')
        axes[1, 0].set_title('Rating Density')
        axes[1, 0].set_xlabel('Rating')
        
        # Rating categories
        categories = {
            'Excellent (8+)': (ratings >= 8.0).sum(),
            'Good (6-8)': ((ratings >= 6.0) & (ratings < 8.0)).sum(),
            'Average (4-6)': ((ratings >= 4.0) & (ratings < 6.0)).sum(),
            'Poor (<4)': (ratings < 4.0).sum()
        }
        
        axes[1, 1].pie(categories.values(), labels=categories.keys(), autopct='%1.1f%%')
        axes[1, 1].set_title('Rating Categories')
        
        plt.tight_layout()
        plt.savefig(self.viz_dir / 'movie_ratings.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_genre_analysis(self, df: pd.DataFrame):
        """Plot genre analysis"""
        if 'genres' not in df.columns:
            logger.warning("No genre data available for visualization")
            return
        
        # Extract all genres
        all_genres = []
        for genres in df['genres'].dropna():
            if isinstance(genres, list):
                all_genres.extend(genres)
            elif isinstance(genres, str):
                all_genres.extend([g.strip() for g in genres.split(',')])
        
        genre_counts = pd.Series(all_genres).value_counts().head(15)
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Movie Genre Analysis', fontsize=16, fontweight='bold')
        
        # Bar plot
        genre_counts.plot(kind='bar', ax=axes[0, 0], color='lightcoral')
        axes[0, 0].set_title('Top 15 Genres')
        axes[0, 0].set_xlabel('Genre')
        axes[0, 0].set_ylabel('Count')
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # Horizontal bar plot
        genre_counts.plot(kind='barh', ax=axes[0, 1], color='lightgreen')
        axes[0, 1].set_title('Top 15 Genres (Horizontal)')
        axes[0, 1].set_xlabel('Count')
        
        # Pie chart for top 10
        top_10_genres = genre_counts.head(10)
        axes[1, 0].pie(top_10_genres.values, labels=top_10_genres.index, autopct='%1.1f%%')
        axes[1, 0].set_title('Top 10 Genres Distribution')
        
        # Genre word cloud
        if all_genres:
            genre_text = ' '.join(all_genres)
            wordcloud = WordCloud(width=400, height=300, background_color='white').generate(genre_text)
            axes[1, 1].imshow(wordcloud, interpolation='bilinear')
            axes[1, 1].axis('off')
            axes[1, 1].set_title('Genre Word Cloud')
        
        plt.tight_layout()
        plt.savefig(self.viz_dir / 'movie_genres.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_year_distribution(self, df: pd.DataFrame):
        """Plot year distribution"""
        if 'year' not in df.columns:
            logger.warning("No year data available for visualization")
            return
        
        years = df['year'].dropna()
        year_counts = years.value_counts().sort_index()
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Movie Release Year Analysis', fontsize=16, fontweight='bold')
        
        # Line plot
        year_counts.plot(kind='line', ax=axes[0, 0], marker='o', color='blue')
        axes[0, 0].set_title('Movies by Release Year')
        axes[0, 0].set_xlabel('Year')
        axes[0, 0].set_ylabel('Number of Movies')
        
        # Bar plot
        year_counts.plot(kind='bar', ax=axes[0, 1], color='orange')
        axes[0, 1].set_title('Movies by Release Year (Bar)')
        axes[0, 1].set_xlabel('Year')
        axes[0, 1].set_ylabel('Number of Movies')
        
        # Histogram
        axes[1, 0].hist(years, bins=20, alpha=0.7, color='green', edgecolor='black')
        axes[1, 0].set_title('Year Distribution Histogram')
        axes[1, 0].set_xlabel('Year')
        axes[1, 0].set_ylabel('Frequency')
        
        # Summary statistics
        stats_text = f"""
        Total Movies: {len(years)}
        Year Range: {years.min()} - {years.max()}
        Most Common Year: {years.mode().iloc[0] if not years.mode().empty else 'N/A'}
        Average Year: {years.mean():.1f}
        """
        axes[1, 1].text(0.1, 0.5, stats_text, fontsize=12, verticalalignment='center')
        axes[1, 1].axis('off')
        axes[1, 1].set_title('Year Statistics')
        
        plt.tight_layout()
        plt.savefig(self.viz_dir / 'movie_years.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_budget_revenue(self, df: pd.DataFrame):
        """Plot budget vs revenue analysis"""
        if 'budget' not in df.columns or 'revenue' not in df.columns:
            logger.warning("Budget or revenue data not available for visualization")
            return
        
        # Filter valid data
        valid_data = df[(df['budget'] > 0) & (df['revenue'] > 0)]
        
        if len(valid_data) == 0:
            logger.warning("No valid budget/revenue data for visualization")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Budget vs Revenue Analysis', fontsize=16, fontweight='bold')
        
        # Scatter plot
        axes[0, 0].scatter(valid_data['budget'], valid_data['revenue'], alpha=0.6)
        axes[0, 0].set_xlabel('Budget ($)')
        axes[0, 0].set_ylabel('Revenue ($)')
        axes[0, 0].set_title('Budget vs Revenue')
        
        # Log scale scatter plot
        axes[0, 1].scatter(valid_data['budget'], valid_data['revenue'], alpha=0.6)
        axes[0, 1].set_xscale('log')
        axes[0, 1].set_yscale('log')
        axes[0, 1].set_xlabel('Budget ($) - Log Scale')
        axes[0, 1].set_ylabel('Revenue ($) - Log Scale')
        axes[0, 1].set_title('Budget vs Revenue (Log Scale)')
        
        # ROI distribution
        roi = (valid_data['revenue'] - valid_data['budget']) / valid_data['budget'] * 100
        axes[1, 0].hist(roi, bins=20, alpha=0.7, color='purple', edgecolor='black')
        axes[1, 0].set_xlabel('ROI (%)')
        axes[1, 0].set_ylabel('Frequency')
        axes[1, 0].set_title('Return on Investment Distribution')
        
        # Budget categories
        budget_categories = {
            'Low (<$10M)': (valid_data['budget'] < 10_000_000).sum(),
            'Medium ($10M-$50M)': ((valid_data['budget'] >= 10_000_000) & 
                                  (valid_data['budget'] < 50_000_000)).sum(),
            'High ($50M-$100M)': ((valid_data['budget'] >= 50_000_000) & 
                                 (valid_data['budget'] < 100_000_000)).sum(),
            'Very High (>$100M)': (valid_data['budget'] >= 100_000_000).sum()
        }
        
        axes[1, 1].pie(budget_categories.values(), labels=budget_categories.keys(), autopct='%1.1f%%')
        axes[1, 1].set_title('Budget Categories')
        
        plt.tight_layout()
        plt.savefig(self.viz_dir / 'budget_revenue.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_runtime_analysis(self, df: pd.DataFrame):
        """Plot runtime analysis"""
        if 'runtime' not in df.columns:
            logger.warning("No runtime data available for visualization")
            return
        
        runtime = df['runtime'].dropna()
        runtime = runtime[runtime > 0]
        
        if len(runtime) == 0:
            logger.warning("No valid runtime data for visualization")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Movie Runtime Analysis', fontsize=16, fontweight='bold')
        
        # Histogram
        axes[0, 0].hist(runtime, bins=20, alpha=0.7, color='teal', edgecolor='black')
        axes[0, 0].set_xlabel('Runtime (minutes)')
        axes[0, 0].set_ylabel('Frequency')
        axes[0, 0].set_title('Runtime Distribution')
        
        # Box plot
        axes[0, 1].boxplot(runtime)
        axes[0, 1].set_ylabel('Runtime (minutes)')
        axes[0, 1].set_title('Runtime Box Plot')
        
        # Runtime categories
        categories = {
            'Short (<90 min)': (runtime < 90).sum(),
            'Standard (90-120 min)': ((runtime >= 90) & (runtime < 120)).sum(),
            'Long (120-180 min)': ((runtime >= 120) & (runtime < 180)).sum(),
            'Very Long (>180 min)': (runtime >= 180).sum()
        }
        
        axes[1, 0].pie(categories.values(), labels=categories.keys(), autopct='%1.1f%%')
        axes[1, 0].set_title('Runtime Categories')
        
        # Statistics
        stats_text = f"""
        Total Movies: {len(runtime)}
        Average Runtime: {runtime.mean():.1f} min
        Median Runtime: {runtime.median():.1f} min
        Shortest: {runtime.min()} min
        Longest: {runtime.max()} min
        """
        axes[1, 1].text(0.1, 0.5, stats_text, fontsize=12, verticalalignment='center')
        axes[1, 1].axis('off')
        axes[1, 1].set_title('Runtime Statistics')
        
        plt.tight_layout()
        plt.savefig(self.viz_dir / 'movie_runtime.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_movie_dashboard(self, df: pd.DataFrame):
        """Create interactive movie dashboard"""
        try:
            # Create subplots
            fig = make_subplots(
                rows=2, cols=2,
                subplot_titles=('Rating Distribution', 'Genre Distribution', 
                              'Year Distribution', 'Budget vs Revenue'),
                specs=[[{"secondary_y": False}, {"type": "pie"}],
                       [{"secondary_y": False}, {"secondary_y": False}]]
            )
            
            # Rating histogram
            if 'rating' in df.columns:
                ratings = df['rating'].dropna()
                fig.add_trace(
                    go.Histogram(x=ratings, name="Ratings", nbinsx=20),
                    row=1, col=1
                )
            
            # Genre pie chart
            if 'genres' in df.columns:
                all_genres = []
                for genres in df['genres'].dropna():
                    if isinstance(genres, list):
                        all_genres.extend(genres)
                
                if all_genres:
                    genre_counts = pd.Series(all_genres).value_counts().head(10)
                    fig.add_trace(
                        go.Pie(labels=genre_counts.index, values=genre_counts.values, name="Genres"),
                        row=1, col=2
                    )
            
            # Year distribution
            if 'year' in df.columns:
                years = df['year'].dropna()
                year_counts = years.value_counts().sort_index()
                fig.add_trace(
                    go.Scatter(x=year_counts.index, y=year_counts.values, 
                             mode='lines+markers', name="Movies by Year"),
                    row=2, col=1
                )
            
            # Budget vs Revenue
            if 'budget' in df.columns and 'revenue' in df.columns:
                valid_data = df[(df['budget'] > 0) & (df['revenue'] > 0)]
                if len(valid_data) > 0:
                    fig.add_trace(
                        go.Scatter(x=valid_data['budget'], y=valid_data['revenue'],
                                 mode='markers', name="Budget vs Revenue"),
                        row=2, col=2
                    )
            
            fig.update_layout(height=800, showlegend=True, title_text="Movie Data Dashboard")
            fig.write_html(self.viz_dir / 'movie_dashboard.html')
            
        except Exception as e:
            logger.error(f"Error creating interactive dashboard: {str(e)}")
    
    def create_review_visualizations(self, reviews_data: List[Dict]):
        """Create comprehensive review visualizations"""
        logger.info("Creating review visualizations")
        
        df = pd.DataFrame(reviews_data)
        
        # Text length analysis
        self._plot_text_analysis(df)
        
        # Rating analysis
        self._plot_review_ratings(df)
        
        # Source analysis
        self._plot_source_analysis(df)
        
        # Word cloud
        self._create_review_wordcloud(df)
    
    def _plot_text_analysis(self, df: pd.DataFrame):
        """Plot review text analysis"""
        if 'text' not in df.columns:
            logger.warning("No text data available for visualization")
            return
        
        texts = df['text'].dropna()
        text_lengths = texts.str.len()
        word_counts = texts.str.split().str.len()
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Review Text Analysis', fontsize=16, fontweight='bold')
        
        # Text length histogram
        axes[0, 0].hist(text_lengths, bins=30, alpha=0.7, color='lightblue', edgecolor='black')
        axes[0, 0].set_xlabel('Text Length (characters)')
        axes[0, 0].set_ylabel('Frequency')
        axes[0, 0].set_title('Text Length Distribution')
        
        # Word count histogram
        axes[0, 1].hist(word_counts, bins=30, alpha=0.7, color='lightgreen', edgecolor='black')
        axes[0, 1].set_xlabel('Word Count')
        axes[0, 1].set_ylabel('Frequency')
        axes[0, 1].set_title('Word Count Distribution')
        
        # Text length categories
        categories = {
            'Short (<100)': (text_lengths < 100).sum(),
            'Medium (100-500)': ((text_lengths >= 100) & (text_lengths < 500)).sum(),
            'Long (500-1000)': ((text_lengths >= 500) & (text_lengths < 1000)).sum(),
            'Very Long (>1000)': (text_lengths >= 1000).sum()
        }
        
        axes[1, 0].pie(categories.values(), labels=categories.keys(), autopct='%1.1f%%')
        axes[1, 0].set_title('Text Length Categories')
        
        # Statistics
        stats_text = f"""
        Total Reviews: {len(texts)}
        Avg Text Length: {text_lengths.mean():.0f} chars
        Avg Word Count: {word_counts.mean():.0f} words
        Median Text Length: {text_lengths.median():.0f} chars
        Median Word Count: {word_counts.median():.0f} words
        """
        axes[1, 1].text(0.1, 0.5, stats_text, fontsize=12, verticalalignment='center')
        axes[1, 1].axis('off')
        axes[1, 1].set_title('Text Statistics')
        
        plt.tight_layout()
        plt.savefig(self.viz_dir / 'review_text_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_review_ratings(self, df: pd.DataFrame):
        """Plot review ratings analysis"""
        if 'rating' not in df.columns:
            logger.warning("No rating data available for visualization")
            return
        
        ratings = df['rating'].dropna()
        
        if len(ratings) == 0:
            logger.warning("No valid rating data for visualization")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Review Rating Analysis', fontsize=16, fontweight='bold')
        
        # Rating distribution
        rating_counts = ratings.value_counts().sort_index()
        rating_counts.plot(kind='bar', ax=axes[0, 0], color='coral')
        axes[0, 0].set_title('Rating Distribution')
        axes[0, 0].set_xlabel('Rating')
        axes[0, 0].set_ylabel('Count')
        
        # Rating histogram
        axes[0, 1].hist(ratings, bins=10, alpha=0.7, color='skyblue', edgecolor='black')
        axes[0, 1].set_xlabel('Rating')
        axes[0, 1].set_ylabel('Frequency')
        axes[0, 1].set_title('Rating Histogram')
        
        # Box plot
        axes[1, 0].boxplot(ratings)
        axes[1, 0].set_ylabel('Rating')
        axes[1, 0].set_title('Rating Box Plot')
        
        # Statistics
        stats_text = f"""
        Total Rated Reviews: {len(ratings)}
        Average Rating: {ratings.mean():.2f}
        Median Rating: {ratings.median():.2f}
        Rating Range: {ratings.min()} - {ratings.max()}
        Most Common: {ratings.mode().iloc[0] if not ratings.mode().empty else 'N/A'}
        """
        axes[1, 1].text(0.1, 0.5, stats_text, fontsize=12, verticalalignment='center')
        axes[1, 1].axis('off')
        axes[1, 1].set_title('Rating Statistics')
        
        plt.tight_layout()
        plt.savefig(self.viz_dir / 'review_ratings.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_source_analysis(self, df: pd.DataFrame):
        """Plot review source analysis"""
        if 'source' not in df.columns:
            logger.warning("No source data available for visualization")
            return
        
        sources = df['source'].value_counts()
        
        fig, axes = plt.subplots(1, 2, figsize=(12, 6))
        fig.suptitle('Review Source Analysis', fontsize=16, fontweight='bold')
        
        # Bar plot
        sources.plot(kind='bar', ax=axes[0], color='lightgreen')
        axes[0].set_title('Reviews by Source')
        axes[0].set_xlabel('Source')
        axes[0].set_ylabel('Count')
        axes[0].tick_params(axis='x', rotation=45)
        
        # Pie chart
        axes[1].pie(sources.values, labels=sources.index, autopct='%1.1f%%')
        axes[1].set_title('Source Distribution')
        
        plt.tight_layout()
        plt.savefig(self.viz_dir / 'review_sources.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_review_wordcloud(self, df: pd.DataFrame):
        """Create word cloud from review texts"""
        if 'text' not in df.columns:
            logger.warning("No text data available for word cloud")
            return
        
        try:
            # Combine all review texts
            all_text = ' '.join(df['text'].dropna().astype(str))
            
            # Create word cloud
            wordcloud = WordCloud(
                width=800, height=400, 
                background_color='white',
                max_words=100,
                colormap='viridis'
            ).generate(all_text)
            
            plt.figure(figsize=(12, 6))
            plt.imshow(wordcloud, interpolation='bilinear')
            plt.axis('off')
            plt.title('Review Text Word Cloud', fontsize=16, fontweight='bold')
            plt.tight_layout()
            plt.savefig(self.viz_dir / 'review_wordcloud.png', dpi=300, bbox_inches='tight')
            plt.close()
            
        except Exception as e:
            logger.error(f"Error creating word cloud: {str(e)}")
    
    def create_visualizations(self, movies_data: List[Dict], reviews_data: List[Dict]):
        """Create all visualizations"""
        logger.info("Creating comprehensive visualizations")
        
        # Movie visualizations
        if movies_data:
            self.create_movie_visualizations(movies_data)
        
        # Review visualizations
        if reviews_data:
            self.create_review_visualizations(reviews_data)
        
        logger.info(f"All visualizations saved to {self.viz_dir}")

if __name__ == "__main__":
    # Configure logging for standalone execution
    logging.basicConfig(level=logging.INFO)
    
    visualizer = DataVisualizer()
    
    # Example usage with dummy data
    dummy_movies = [
        {'title': 'Movie 1', 'year': 2025, 'rating': 8.5, 'genres': ['Action', 'Drama']},
        {'title': 'Movie 2', 'year': 2025, 'rating': 7.2, 'genres': ['Comedy']}
    ]
    
    dummy_reviews = [
        {'text': 'Great movie with excellent acting!', 'rating': 9, 'source': 'imdb'},
        {'text': 'Not bad, but could be better.', 'rating': 6, 'source': 'tmdb'}
    ]
    
    visualizer.create_visualizations(dummy_movies, dummy_reviews)
    print("Visualizations created successfully")
