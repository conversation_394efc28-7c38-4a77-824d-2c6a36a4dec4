"""
Text Preprocessing Module
Handles cleaning and preprocessing of movie reviews and text data
"""

import pandas as pd
import numpy as np
import json
import logging
from typing import List, Dict, Optional
import nltk
import spacy
from pathlib import Path
import sys
import re

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from config import RAW_DATA_DIR, PROCESSED_DATA_DIR, LANGUAGES, STOP_WORDS_REMOVE, LEMMATIZATION
from 02_data_cleansing.utils.cleaning_utils import TextCleaner, DataValidator, remove_duplicates

logger = logging.getLogger(__name__)

class TextPreprocessor:
    """Main class for text preprocessing operations"""
    
    def __init__(self):
        self.text_cleaner = TextCleaner()
        self.validator = DataValidator()
        self.nlp = None
        self.stop_words = set()
        
        # Initialize NLP tools
        self._initialize_nlp_tools()
    
    def _initialize_nlp_tools(self):
        """Initialize NLTK and spaCy tools"""
        try:
            # Download required NLTK data
            nltk.download('punkt', quiet=True)
            nltk.download('stopwords', quiet=True)
            nltk.download('wordnet', quiet=True)
            nltk.download('averaged_perceptron_tagger', quiet=True)
            
            from nltk.corpus import stopwords
            self.stop_words = set(stopwords.words('english'))
            
            # Load spaCy model
            try:
                self.nlp = spacy.load('en_core_web_sm')
            except OSError:
                logger.warning("spaCy English model not found. Install with: python -m spacy download en_core_web_sm")
                self.nlp = None
                
        except Exception as e:
            logger.error(f"Error initializing NLP tools: {str(e)}")
    
    def preprocess_text(self, text: str, 
                       clean_html: bool = True,
                       expand_contractions: bool = True,
                       remove_stopwords: bool = STOP_WORDS_REMOVE,
                       lemmatize: bool = LEMMATIZATION,
                       lowercase: bool = True) -> str:
        """Comprehensive text preprocessing"""
        
        if not text or not isinstance(text, str):
            return ""
        
        # Basic cleaning
        processed_text = self.text_cleaner.clean_text_comprehensive(
            text,
            expand_contractions=expand_contractions,
            remove_html=clean_html,
            remove_urls=True,
            remove_emails=True,
            normalize_unicode=True,
            remove_special_chars=False,
            keep_punctuation=True
        )
        
        # Convert to lowercase
        if lowercase:
            processed_text = processed_text.lower()
        
        # Advanced NLP preprocessing
        if self.nlp:
            processed_text = self._advanced_preprocessing(
                processed_text, 
                remove_stopwords, 
                lemmatize
            )
        else:
            # Fallback to basic preprocessing
            processed_text = self._basic_preprocessing(
                processed_text, 
                remove_stopwords, 
                lemmatize
            )
        
        return processed_text.strip()
    
    def _advanced_preprocessing(self, text: str, remove_stopwords: bool, lemmatize: bool) -> str:
        """Advanced preprocessing using spaCy"""
        try:
            doc = self.nlp(text)
            
            processed_tokens = []
            for token in doc:
                # Skip punctuation and whitespace
                if token.is_punct or token.is_space:
                    continue
                
                # Skip stop words if requested
                if remove_stopwords and token.is_stop:
                    continue
                
                # Use lemma if requested, otherwise use original token
                if lemmatize and token.lemma_:
                    processed_tokens.append(token.lemma_)
                else:
                    processed_tokens.append(token.text)
            
            return ' '.join(processed_tokens)
            
        except Exception as e:
            logger.warning(f"Error in advanced preprocessing: {str(e)}")
            return self._basic_preprocessing(text, remove_stopwords, lemmatize)
    
    def _basic_preprocessing(self, text: str, remove_stopwords: bool, lemmatize: bool) -> str:
        """Basic preprocessing using NLTK"""
        try:
            from nltk.tokenize import word_tokenize
            from nltk.stem import WordNetLemmatizer
            
            # Tokenize
            tokens = word_tokenize(text)
            
            # Remove stop words
            if remove_stopwords:
                tokens = [token for token in tokens if token.lower() not in self.stop_words]
            
            # Lemmatize
            if lemmatize:
                lemmatizer = WordNetLemmatizer()
                tokens = [lemmatizer.lemmatize(token) for token in tokens]
            
            # Filter out short tokens and punctuation
            tokens = [token for token in tokens if len(token) > 2 and token.isalpha()]
            
            return ' '.join(tokens)
            
        except Exception as e:
            logger.warning(f"Error in basic preprocessing: {str(e)}")
            return text
    
    def preprocess_reviews(self, reviews_data: List[Dict]) -> List[Dict]:
        """Preprocess all reviews data"""
        logger.info(f"Preprocessing {len(reviews_data)} reviews")
        
        processed_reviews = []
        
        for i, review in enumerate(reviews_data):
            if i % 100 == 0:
                logger.info(f"Processing review {i+1}/{len(reviews_data)}")
            
            try:
                # Validate review data
                validated_review = self.validator.validate_review_data(review)
                
                # Skip invalid reviews
                if not self.validator.is_valid_review(validated_review):
                    continue
                
                # Preprocess review text
                original_text = validated_review.get('text', '')
                processed_text = self.preprocess_text(original_text)
                
                # Skip if preprocessing resulted in empty text
                if not processed_text.strip():
                    continue
                
                # Create processed review
                processed_review = validated_review.copy()
                processed_review['original_text'] = original_text
                processed_review['processed_text'] = processed_text
                processed_review['text_length'] = len(original_text)
                processed_review['processed_length'] = len(processed_text)
                processed_review['word_count'] = len(processed_text.split())
                
                # Extract additional features
                processed_review.update(self._extract_text_features(original_text))
                
                processed_reviews.append(processed_review)
                
            except Exception as e:
                logger.warning(f"Error processing review {i}: {str(e)}")
                continue
        
        # Remove duplicates
        processed_reviews = remove_duplicates(
            processed_reviews, 
            ['processed_text', 'imdb_id', 'tmdb_id']
        )
        
        logger.info(f"Preprocessing completed. {len(processed_reviews)} reviews processed successfully")
        
        # Save processed reviews
        self.save_processed_reviews(processed_reviews)
        
        return processed_reviews
    
    def _extract_text_features(self, text: str) -> Dict:
        """Extract additional features from text"""
        features = {}
        
        try:
            # Basic statistics
            features['sentence_count'] = len(re.split(r'[.!?]+', text))
            features['exclamation_count'] = text.count('!')
            features['question_count'] = text.count('?')
            features['uppercase_ratio'] = sum(1 for c in text if c.isupper()) / len(text) if text else 0
            
            # Sentiment indicators (basic)
            positive_words = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'love', 'best']
            negative_words = ['bad', 'terrible', 'awful', 'horrible', 'worst', 'hate', 'disappointing']
            
            text_lower = text.lower()
            features['positive_word_count'] = sum(1 for word in positive_words if word in text_lower)
            features['negative_word_count'] = sum(1 for word in negative_words if word in text_lower)
            
        except Exception as e:
            logger.warning(f"Error extracting text features: {str(e)}")
        
        return features
    
    def clean_movie_data(self, movies_data: List[Dict]) -> List[Dict]:
        """Clean and validate movie metadata"""
        logger.info(f"Cleaning {len(movies_data)} movies data")
        
        cleaned_movies = []
        
        for movie in movies_data:
            try:
                # Validate movie data
                validated_movie = self.validator.validate_movie_data(movie)
                
                # Skip movies without title
                if not validated_movie.get('title'):
                    continue
                
                # Clean text fields
                if 'overview' in validated_movie:
                    validated_movie['overview'] = self.text_cleaner.clean_text_comprehensive(
                        validated_movie['overview']
                    )
                
                if 'plot' in validated_movie:
                    validated_movie['plot'] = self.text_cleaner.clean_text_comprehensive(
                        validated_movie['plot']
                    )
                
                cleaned_movies.append(validated_movie)
                
            except Exception as e:
                logger.warning(f"Error cleaning movie data: {str(e)}")
                continue
        
        # Remove duplicates
        cleaned_movies = remove_duplicates(
            cleaned_movies, 
            ['title', 'year', 'imdb_id']
        )
        
        logger.info(f"Movie data cleaning completed. {len(cleaned_movies)} movies cleaned")
        
        # Save cleaned movies
        self.save_cleaned_movies(cleaned_movies)
        
        return cleaned_movies
    
    def save_processed_reviews(self, reviews_data: List[Dict], filename: str = 'processed_reviews.json'):
        """Save processed reviews data"""
        filepath = PROCESSED_DATA_DIR / filename
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(reviews_data, f, indent=2, ensure_ascii=False)
            
            # Also save as CSV
            df = pd.DataFrame(reviews_data)
            csv_filepath = filepath.with_suffix('.csv')
            df.to_csv(csv_filepath, index=False, encoding='utf-8')
            
            logger.info(f"Saved processed reviews to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving processed reviews: {str(e)}")
    
    def save_cleaned_movies(self, movies_data: List[Dict], filename: str = 'cleaned_movies.json'):
        """Save cleaned movies data"""
        filepath = PROCESSED_DATA_DIR / filename
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(movies_data, f, indent=2, ensure_ascii=False)
            
            # Also save as CSV
            df = pd.DataFrame(movies_data)
            csv_filepath = filepath.with_suffix('.csv')
            df.to_csv(csv_filepath, index=False, encoding='utf-8')
            
            logger.info(f"Saved cleaned movies to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving cleaned movies: {str(e)}")

if __name__ == "__main__":
    # Configure logging for standalone execution
    logging.basicConfig(level=logging.INFO)
    
    processor = TextPreprocessor()
    
    # Example usage
    sample_text = "This is a great movie! I really loved the acting and the plot was amazing."
    processed = processor.preprocess_text(sample_text)
    print(f"Original: {sample_text}")
    print(f"Processed: {processed}")
