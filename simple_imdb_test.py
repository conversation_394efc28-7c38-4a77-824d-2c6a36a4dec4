#!/usr/bin/env python3
"""
Simple IMDB Test Script
Tests the working IMDB endpoints we discovered
"""

import sys
import time
import requests
import pandas as pd
import json
from pathlib import Path
from datetime import datetime
import cloudscraper
from bs4 import BeautifulSoup
import re

# Add project root to path
sys.path.append(str(Path(__file__).parent))

try:
    from config import RAW_DATA_DIR
except ImportError:
    RAW_DATA_DIR = Path("data/raw")

class SimpleIMDBTest:
    """Simple test of working IMDB endpoints"""
    
    def __init__(self):
        self.base_url = "https://www.imdb.com"
        self.mobile_url = "https://m.imdb.com"
        self.datasets_url = "https://datasets.imdbws.com"
        
        # Initialize CloudScraper
        self.scraper = cloudscraper.create_scraper()
        
        # Ensure data directory exists
        RAW_DATA_DIR.mkdir(parents=True, exist_ok=True)
        
        print("🎭 Simple IMDB Test Initialized")
    
    def test_mobile_imdb_access(self):
        """Test mobile IMDB access with known movies"""
        print("\n📱 Testing Mobile IMDB Access...")
        print("=" * 50)
        
        # Test with popular movies
        test_movies = [
            'tt0111161',  # Shawshank Redemption
            'tt0137523',  # Fight Club
            'tt0068646',  # The Godfather
        ]
        
        successful_scrapes = []
        
        for movie_id in test_movies:
            print(f"\n🔍 Testing: {movie_id}")
            
            try:
                url = f"{self.mobile_url}/title/{movie_id}/"
                response = self.scraper.get(url, timeout=15)
                
                print(f"   Status: {response.status_code}")
                
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    # Try to extract basic info
                    movie_data = self.extract_basic_info(soup, movie_id)
                    
                    if movie_data:
                        successful_scrapes.append(movie_data)
                        print(f"   ✅ Title: {movie_data.get('title', 'Unknown')}")
                        print(f"   ✅ Rating: {movie_data.get('rating', 'N/A')}")
                        print(f"   ✅ Year: {movie_data.get('year', 'N/A')}")
                    else:
                        print("   ⚠️ Could not extract data")
                else:
                    print(f"   ❌ Failed with status {response.status_code}")
                
                time.sleep(2)  # Rate limiting
                
            except Exception as e:
                print(f"   ❌ Error: {str(e)}")
        
        print(f"\n📊 Results: {len(successful_scrapes)}/{len(test_movies)} successful")
        return successful_scrapes
    
    def extract_basic_info(self, soup, movie_id):
        """Extract basic movie information from mobile IMDB"""
        try:
            movie_data = {'imdb_id': movie_id}
            
            # Title - try multiple selectors
            title_elem = (soup.find('h1') or 
                         soup.find('title') or 
                         soup.find('span', class_='titlereference-title'))
            
            if title_elem:
                title = title_elem.get_text().strip()
                # Clean title
                title = re.sub(r'\s*\(\d{4}\).*', '', title)
                title = re.sub(r'\s*-\s*IMDb.*', '', title)
                movie_data['title'] = title
            
            # Rating - try multiple patterns
            rating_patterns = [
                r'(\d+\.?\d*)/10',
                r'Rating:\s*(\d+\.?\d*)',
                r'★\s*(\d+\.?\d*)'
            ]
            
            page_text = soup.get_text()
            for pattern in rating_patterns:
                rating_match = re.search(pattern, page_text)
                if rating_match:
                    try:
                        movie_data['rating'] = float(rating_match.group(1))
                        break
                    except ValueError:
                        continue
            
            # Year
            year_match = re.search(r'\b(19|20)\d{2}\b', page_text)
            if year_match:
                movie_data['year'] = int(year_match.group())
            
            # Genres
            genre_links = soup.find_all('a', href=re.compile(r'/genre/'))
            if genre_links:
                genres = [link.get_text().strip() for link in genre_links[:3]]
                movie_data['genres'] = genres
            
            return movie_data if len(movie_data) > 1 else None
            
        except Exception as e:
            print(f"   ⚠️ Extraction error: {str(e)}")
            return None
    
    def test_imdb_datasets_access(self):
        """Test access to IMDB official datasets"""
        print("\n📊 Testing IMDB Datasets Access...")
        print("=" * 50)
        
        # Test dataset availability
        datasets = [
            'title.basics.tsv.gz',
            'title.ratings.tsv.gz'
        ]
        
        accessible_datasets = []
        
        for dataset in datasets:
            print(f"\n🔍 Testing: {dataset}")
            
            try:
                url = f"{self.datasets_url}/{dataset}"
                response = requests.head(url, timeout=10)
                
                if response.status_code == 200:
                    size = response.headers.get('content-length')
                    if size:
                        size_mb = int(size) / (1024 * 1024)
                        print(f"   ✅ Available - Size: {size_mb:.1f} MB")
                    else:
                        print(f"   ✅ Available")
                    accessible_datasets.append(dataset)
                else:
                    print(f"   ❌ Not accessible ({response.status_code})")
                    
            except Exception as e:
                print(f"   ❌ Error: {str(e)}")
        
        print(f"\n📊 Results: {len(accessible_datasets)}/{len(datasets)} datasets accessible")
        return accessible_datasets
    
    def collect_sample_data(self, scraped_movies):
        """Collect and save sample data"""
        print("\n💾 Collecting Sample Data...")
        print("=" * 50)
        
        if not scraped_movies:
            print("❌ No scraped movies to save")
            return None
        
        # Create DataFrame
        df = pd.DataFrame(scraped_movies)
        
        # Save to CSV
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = RAW_DATA_DIR / f"imdb_sample_data_{timestamp}.csv"
        
        df.to_csv(filename, index=False)
        print(f"✅ Saved sample data: {filename}")
        
        # Display summary
        print(f"\n📊 Sample Data Summary:")
        print(f"   Movies: {len(df)}")
        print(f"   Columns: {list(df.columns)}")
        
        if 'rating' in df.columns:
            ratings = df['rating'].dropna()
            if len(ratings) > 0:
                print(f"   Avg Rating: {ratings.mean():.1f}")
        
        if 'year' in df.columns:
            years = df['year'].dropna()
            if len(years) > 0:
                print(f"   Year Range: {years.min()} - {years.max()}")
        
        return filename
    
    def run_comprehensive_test(self):
        """Run comprehensive test of IMDB access"""
        print("🧪 IMDB Comprehensive Access Test")
        print("=" * 60)
        print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        results = {}
        
        # Test 1: Mobile IMDB scraping
        scraped_movies = self.test_mobile_imdb_access()
        results['mobile_scraping'] = len(scraped_movies) > 0
        
        # Test 2: Datasets access
        accessible_datasets = self.test_imdb_datasets_access()
        results['datasets_access'] = len(accessible_datasets) > 0
        
        # Test 3: Save sample data
        if scraped_movies:
            sample_file = self.collect_sample_data(scraped_movies)
            results['data_collection'] = sample_file is not None
        else:
            results['data_collection'] = False
        
        # Summary
        print("\n" + "=" * 60)
        print("🏁 Test Summary")
        print("=" * 60)
        
        for test_name, success in results.items():
            status = "✅ PASSED" if success else "❌ FAILED"
            print(f"{test_name.upper().replace('_', ' ')}: {status}")
        
        total_passed = sum(results.values())
        total_tests = len(results)
        
        print(f"\nOverall: {total_passed}/{total_tests} tests passed")
        
        # Recommendations
        print("\n💡 Recommendations:")
        
        if results['mobile_scraping']:
            print("✅ Mobile IMDB scraping is working!")
            print("   - Can extract basic movie information")
            print("   - Use rate limiting and respectful scraping")
        else:
            print("❌ Mobile IMDB scraping blocked")
        
        if results['datasets_access']:
            print("✅ IMDB datasets are accessible!")
            print("   - Download official datasets for comprehensive data")
            print("   - Combine with TMDB API for reviews")
        else:
            print("❌ IMDB datasets not accessible")
        
        if results['data_collection']:
            print("✅ Data collection pipeline working!")
        else:
            print("⚠️ Data collection needs improvement")
        
        # Final recommendation
        if total_passed >= 2:
            print("\n🎉 IMDB data collection is feasible!")
            print("💡 Recommended approach:")
            print("   1. Use TMDB API for primary data (already working)")
            print("   2. Supplement with mobile IMDB scraping")
            print("   3. Download IMDB datasets for additional metadata")
        else:
            print("\n⚠️ IMDB access is limited")
            print("💡 Focus on TMDB API as primary data source")
        
        return results

def main():
    """Main execution function"""
    tester = SimpleIMDBTest()
    tester.run_comprehensive_test()

if __name__ == "__main__":
    main()
